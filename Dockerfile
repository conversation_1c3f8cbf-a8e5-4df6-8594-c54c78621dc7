# Use Bun's official Docker image
FROM oven/bun:1.1.20 AS build

WORKDIR /src

# Copy over package.json and .env
COPY --link package.json  ./
COPY --link bunfig.toml  ./
COPY --link bun.lockb* ./
COPY --link .npmrc ./

# Install dependencies
RUN bun install

# Copy the rest of the app
COPY --link . .

# Build the application using Nuxt
RUN bun run build

# Remove the .npmrc to ensure the token is not included in the final image
RUN rm -f .npmrc

# Stage 2: Run the application using bunx
FROM build AS final

WORKDIR /app

COPY --link --from=build /src/.output ./.output
# COPY --link --from=build /src/.env ./

# Expose the specified port
ENV PORT=8080
EXPOSE $PORT

# Run the built Nuxt app using bunx
CMD ["bun", ".output/server/index.mjs"]
