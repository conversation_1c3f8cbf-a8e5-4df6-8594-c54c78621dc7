# TdA Nuxt šablona

Pokud si nevíš rady s Nuxtem, koukni na [Nuxt 3 dokumentaci](https://nuxt.com/docs/getting-started/introduction).

## Knihovny
- `Pinia` pro state management
- `Tailwind` pro stylovní

## Custom úpravy
- `app.vue` m<PERSON> milou welcome stránku
- `tailwind.config.js` má nastavené brandmanuál barvy a písma
- `.env.example` stačí upravit pro připojení na [FishBush](http://odevzdavani.tourdeapp.cz:1337)
- `./stores/FishBush.ts` a `./server/api/[...path].ts` zajišťují SSR state-management, do kterého stačí velmi jednoduše nacpat libovolný resource z FishBushe

## Další zdroje
- Pokud se nechcete drbat s komponenty, doporučuji přidat [NaiveUI](https://naiveui.com/)
- bun > npm

## Setup

Nainstaluj si všechno pomocí:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Spusť si development server na `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm run dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Sestav appku do production:

```bash
# npm
npm run build

# pnpm
pnpm run build

# yarn
yarn build

# bun
bun run build
```

Lokálně si production build prohlídneš:

```bash
# npm
npm run preview

# pnpm
pnpm run preview

# yarn
yarn preview

# bun
bun run preview
```
