<script setup lang="ts">
import { useI18n } from 'vue-i18n';
const { gtag, initialize } = useGtag()

const { t } = useI18n();
useHead({
	bodyAttrs: {
		class: "bg-background text-white",
	},
	htmlAttrs: {
    lang: t('meta.lang'),
		class:
			"scroll-pt-[86px] scroll-smooth scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar-thin scrollbar-thumb-tda-200 scrollbar-track-[#171b23] overflow-y-scroll",
	}
});

const showConsentBanner = ref(false);

function onConsentGiven() {
  showConsentBanner.value = false;
  initialize();
  gtag('consent', 'update', {
    analytics_storage: 'granted',
  });
}

onMounted(() => {
  const consent = localStorage.getItem('cookieConsent'); //accepted, declined, null
  if (consent) {
    if (consent === 'accepted') {
      onConsentGiven();
    }
  } else {
    showConsentBanner.value = true;
  }
});
</script>

<template>
  <keep-alive>
    <NavigationBar />
  </keep-alive>
  <div class="min-h-screen flex flex-col">
    <main class="pt-20 flex-1">
      <NuxtPage keepalive/>
    </main>
    <keep-alive>
      <FooterBar />
    </keep-alive>
    <CookieConsentBanner v-if="showConsentBanner" @consent-given="onConsentGiven" />
  </div>
</template>
