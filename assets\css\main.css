:root {
  --li-img: url('/bullet_point.webp');
}

a {
  @apply text-tda-300;
  &:hover {
    @apply text-tda;
  }
}

a.text-informative {
  &:hover {
    @apply text-informative/70;
  }
}

p {
  @apply leading-6 pb-4;
}

h1, h2, h3, h4, h5, h6 {
  @apply font-bold mb-2;
}

h1 {
  @apply text-tda-200 text-[2rem]/[1.2] xs:text-[2.5rem]/[1.2] mt-2 [&:not(:first-child)]:mt-8;
}

h2 {
  @apply text-tda-200 text-[2rem]/[1.2] xs:text-[2.5rem]/[1.2] mt-2 [&:not(:first-child)]:mt-6;
}

h3 {
  @apply text-tda-200 text-[1.75rem]/[1.2] xs:text-[2rem]/[1.2] mt-2 [&:not(:first-child)]:mt-4;
}

h4 {
  @apply text-tda-300 text-[1.5rem]/[1.2] xs:text-[1.75rem]/[1.2] mt-2 [&:not(:first-child)]:mt-3;
}

h5 {
  @apply text-tda-400 text-[1.25rem]/[1.2] xs:text-[1.5rem]/[1.2] mt-2;
}

h6 {
  @apply text-tda-500 text-[1.1rem]/[1.2] xs:text-[1.25rem]/[1.2] mt-2;
}

.content {
  @apply py-6;
}

.dark-content {
  @apply bg-tda-300 text-[#ecf0f1] py-6;

  h1 {
    @apply text-informative text-[2.5rem]/[1.2];
  }
  
  h2 {
    @apply text-informative text-[2.5rem]/[1.2];
  }
  
  h3 {
    @apply text-informative text-[2rem]/[1.2];
  }
  
  h4 {
    @apply text-gray-50 text-[1.75rem]/[1.2];
  }
  
  h5 {
    @apply text-gray-100 text-[1.5rem]/[1.2];
  }
  
  h6 {
    @apply text-gray-200 text-[1.25rem]/[1.2];
  }
}

.content-container {
  /* act like a normal container, but with different max-width */
  @apply container max-w-full mx-auto px-4;
  /* at 640px, the max-width is 100% */
  @apply sm:max-w-[610px];
  /* at 768px, the max-width is 720px */
  @apply md:max-w-[720px];
  /* at 1024px, the max-width is 988px */
  @apply lg:max-w-[988px];
  /* at 1280px, the max-width is 1200px */
  @apply xl:max-w-[1200px];
  /* at 1536px, the max-width is 1340px */
  @apply 2xl:max-w-[1340px];
}

.content-container-thin {
  /* act like a normal container, but with different max-width */
  @apply container max-w-full mx-auto px-4;
  /* at 640px, the max-width is 100% */
  @apply sm:max-w-[610px];
  /* at 768px, the max-width is 720px */
  @apply md:max-w-[720px];
  /* at 1024px, the max-width is 910px */
  @apply lg:max-w-[910px];
  /* at 1280px, the max-width is 960px */
  @apply xl:max-w-[960px];
  /* at 1536px, the max-width is 960px */
  @apply 2xl:max-w-[960px];
}

.pre-title {
  @apply text-3xl sm:text-4xl text-[#ecf0f1] container min-w-full;
}

.title {
  @apply sm:text-6xl/[1.2] mt-7 text-informative text-4xl/[1.2];
}

.subtitle {
  @apply font-normal text-informative mb-5 text-2xl/[1.2] sm:text-3xl/[1.2];
}

.btn {
  @apply shadow-md hover:shadow-lg disabled:shadow-md disabled:opacity-50 py-4 px-6 text-sm transition-shadow duration-150 ease-in-out m-1 border-0 rounded-[0.125rem] uppercase whitespace-normal break-words;

  :not(:disabled):not(.disabled) {
    @apply cursor-pointer;
  }

  &.btn-lg {
    @apply py-4 xs:px-16 text-lg w-full xs:w-auto text-center xs:text-start;
  }
  
  &.btn-primary {
    @apply bg-tda-300 text-informative;
    &:not(:disabled):hover {
      @apply bg-tda-300/90 text-informative/70;
    }
  }

  &.btn-secondary {
    @apply bg-[#222] text-tda-300;
    &:not(:disabled):hover {
      @apply bg-[#222]/90 text-tda-300;
    }
  }
}

.btns > .btn-main-action {
  @apply sm:mx-2 sm:w-[250px] mx-auto w-11/12 bg-informative text-tda-300 font-bold inline-block text-xl sm:text-2xl mb-4;
}

.row {
  @apply flex flex-wrap -mx-4;
}

.card {
  @apply bg-white/5 border-2 border-tda-300/10 min-w-full;
}

.tda-shadow {
  @apply transition-shadow;
  box-shadow: none;
}

.tda-shadow:hover {
  box-shadow: 3px 3px 0 theme('colors.tda.300')66,
  6px 6px 0 theme('colors.tda.400')4D,
  9px 9px 0 theme('colors.tda.500')33,
  12px 12px 0 theme('colors.tda.600')1A,
  15px 15px 0 theme('colors.tda.700')0D;
}

ol {
  @apply pl-6 list-none;
  counter-reset: custom-counter;
}

ol > li {
  counter-increment: custom-counter;
  position: relative;
  @apply pl-6;
}

ol > li::before {
  content: counter(custom-counter) ".";
  @apply absolute left-0 font-bold text-tda-300;
}

ul.arrows-small > li {
  background: rgba(0, 0, 0, 0) var(--li-img) left center no-repeat;
  background-size: 1.2rem;
  @apply py-1 pl-10 pr-0 block mb-2;
}

ul.arrows > li {
  background: rgba(0, 0, 0, 0) var(--li-img) left center no-repeat;
  background-size: 25px;
  @apply py-1 pl-10 pr-0 block mb-6;
}

ul.schedule {
  @apply pt-8;
  &>li {
    @apply text-xl/6;
    /* the last one should not have margin */
    &:last-child {
      @apply mb-0;
    }
  }
  .date {
    @apply font-bold text-tda-300;
    &::after {
      @apply pr-4;
      content: ' ';
    }
  }
}