<script setup lang="ts">
import placeholder from "@/public/person.webp";
import { createImageUrls } from "@/utils/dataUtils.js";

const props = defineProps({
	contact: {
		type: Object as PropType<WebContactEntity>,
		required: true,
	},
	showCard: {
		type: Boolean,
		default: true,
	},
});

const altText = computed(() => {
	const user = props.contact.attributes?.user?.data?.attributes;
	const role = props.contact.attributes?.roleName;
	return `${user?.firstname} ${user?.lastname}, ${role}`;
});

const userAttributes = computed(
	() => props.contact.attributes?.user?.data?.attributes,
);

const imageUrls = computed(() => {
	// from the profilePictures array, get the image with projectType.id = 4, otherwise get the first image, if any, if none, return default image
	if (!profilePicture.value) return null;
	try {
		return createImageUrls(
			profilePicture.value?.image?.data as UploadFileEntity,
		);
	} catch (error) {
		return null;
	}
});

// Find the profile picture with the logic first(TdA, SCG, placeholder = undefined)
const profilePicture = computed(() => {
	const pictures =
		props.contact.attributes?.user?.data?.attributes?.profilePictures;

	let pic = pictures?.find((p) => Number(p?.projectType?.data?.id) === 4);

	if (!pic && pictures && pictures.length > 0) {
		pic = pictures[0];
	}

	return pic;
});

const cropData = computed(() => {
	const pic = profilePicture.value;

	// Check if crop data is available
	if (
		!(
			pic &&
			pic.x != null &&
			pic.y != null &&
			pic.width != null &&
			pic.height != null
		)
	)
		return null;

	const image = pic.image?.data?.attributes;
	if (!image) return null;
	if (!image.width || !image.height) return null;

	return {
		cropX: pic.x,
		cropY: pic.y,
		cropHeight: pic.height,
		imageWidth: image.width,
		imageHeight: image.height,
		aspectRatio: pic.width / pic.height,
	};
});

// Assuming we have access to the container's height, which we'll set via CSS variable
// or we can define it in our component
const containerHeight = 128; // Or get this value from somewhere else if it's dynamic

const imgStyle = computed(() => {
	if (!cropData.value) return {};

	const { cropX, cropY, cropHeight, imageWidth, imageHeight } = cropData.value;

	// Scaling factor to make the cropped area fit the container height
	const scale = containerHeight / cropHeight;

	// Scaled image dimensions
	const scaledImageWidth = imageWidth * scale;
	const scaledImageHeight = imageHeight * scale;

	// Translate values to position the cropped area within the container
	const translateX = -cropX * scale;
	const translateY = -cropY * scale;

	return {
		width: `${scaledImageWidth}px`,
		height: `${scaledImageHeight}px`,
		transform: `translate(${translateX}px, ${translateY}px)`,
	};
});

const containerStyle = computed(() => {
	if (!cropData.value)
		return {
			width: `${containerHeight}px`,
			height: `${containerHeight}px`,
		};

	const aspectRatio = cropData.value.aspectRatio;
	const containerWidth = containerHeight * aspectRatio;

	return {
		width: `${containerWidth}px`,
		height: `${containerHeight}px`,
	};
});
</script>

<template>
  <div class="flex" :class="{'card shadow-xl': showCard, 'border-2 border-transparent': !showCard}">
    <div :style="containerStyle" class="relative overflow-hidden">
      <img v-if="imageUrls" :src="imageUrls.defaultImageUrl"
        :srcset="`${imageUrls.smallImageUrl} 480w, ${imageUrls.mediumImageUrl} 800w, ${imageUrls.defaultImageUrl} 1200w`"
        sizes="(max-width: 600px) 480px, (max-width: 1024px) 800px, 1200px" :alt="altText" :style="imgStyle"
        class="absolute top-0 left-0 max-w-none">
      <img v-else :src="placeholder" :alt="altText" class="absolute top-0 left-0">
    </div>
    <div class="flex-1 pl-6 py-2 pr-2">
      <div class="font-extrabold xs:text-xl mb-1">{{ contact.attributes?.roleName }}</div>
      <div class="font-bold text-sm xs:text-base">{{ userAttributes?.firstname }} {{ userAttributes?.lastname }}</div>
      <div class="text-sm xs:text-base" v-if="userAttributes?.email"><a :href="`mailto:${userAttributes?.email}?subject=Tour de App`" class="text-informative">{{ userAttributes?.email }}</a></div>
      <div class="text-sm xs:text-base" v-if="userAttributes?.phoneNumber">{{ userAttributes?.phoneNumber }}</div>
    </div>
  </div>
</template>