<script setup lang="ts">
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const emit = defineEmits(['consent-given']);
const showBanner = ref(true);

onMounted(() => {
  // Check if user has already made a choice
  const consent = localStorage.getItem('cookieConsent');
  if (consent) {
    showBanner.value = false;
  }
});

function acceptCookies() {
  localStorage.setItem('cookieConsent', 'accepted');
  showBanner.value = false;
  emit('consent-given');
}

function declineCookies() {
  localStorage.setItem('cookieConsent', 'declined');
  showBanner.value = false;
}
</script>

<template>
  <div v-if="showBanner" class="cookie-consent-banner flex items-center flex-col sm:flex-row">
    <div class="mr-2">
      {{ t('analytics.banner')}}
    </div>
    <div>
      <button class="btn btn-primary" @click="acceptCookies">{{ t('analytics.accept')}}</button>
      <button class="btn" @click="declineCookies">{{ t('analytics.decline')}}</button>
    </div>
  </div>
</template>


<style>
.cookie-consent-banner {
  @apply fixed bottom-0 w-full bg-background/90 p-4 text-center z-50;
}
</style>
