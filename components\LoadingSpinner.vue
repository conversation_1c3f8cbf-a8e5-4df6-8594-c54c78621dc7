<template>
  <div class="fixed inset-0 flex items-center justify-center bg-background bg-opacity-60 z-50">
    <div class="w-16 h-16 border-t-4 border-b-4 border-tda-500 rounded-full animate-spin">
      <div class="w-full h-full border-t-4 border-b-4 border-tda-400 rounded-full animate-spin">
        <div class="w-full h-full border-t-4 border-b-4 border-tda-300 rounded-full animate-spin">
          <div class="w-full h-full border-t-4 border-b-4 border-tda-600 rounded-full animate-spin"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes custom-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: custom-spin 1s linear infinite;
}

.animate-spin > div {
  animation-duration: 1.5s;
}

.animate-spin > div > div {
  animation-duration: 2s;
}

.animate-spin > div > div > div {
  animation-duration: 2.5s;
}
</style>