<script setup lang="ts">
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { z } from "zod";

const { t } = useI18n();

const props = defineProps<{
  modelValue: {
    name: string;
    email: string;
    street: string;
    city: string;
    zip: string;
    ic: string;
    dic: string;
  };
}>();

const emit = defineEmits<(e: "update:modelValue", value: typeof props.modelValue) => void>();

const invoice = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// Define the schema using Zod
const schema = z.object({
  name: z.string().min(1, { message: t("validation.invoice_name") }),
  email: z.string().email({ message: t("validation.invoice_email") }),
  street: z.string().optional(),
  city: z.string().optional(),
  zip: z.string().optional(),
  ic: z.string().optional(),
  dic: z.string().optional(),
});

const errors = ref({
  name: "",
  email: "",
  street: "",
  city: "",
  zip: "",
  ic: "",
  dic: "",
});

const touched = ref({
  name: false,
  email: false,
  street: false,
  city: false,
  zip: false,
  ic: false,
  dic: false,
});

const invoiceFields = Object.keys(invoice.value) as Array<keyof typeof invoice.value>;

for (const field of invoiceFields) {
  watch(
    () => invoice.value[field],
    () => {
      if (touched.value[field]) {
        validateField(field);
      }
    },
  );
}

const validateField = (field: keyof typeof invoice.value) => {
  if (!touched.value[field]) return;
  const fieldSchema = schema.shape[field];
  const validation = fieldSchema.safeParse(invoice.value[field]);
  if (!validation.success) {
    errors.value[field] = validation.error.errors[0].message;
  } else {
    errors.value[field] = "";
  }
};

const handleInput = (field: keyof typeof invoice.value) => {
  touched.value[field] = true;
  validateField(field);
};

const handleBlur = (field: keyof typeof invoice.value) => {
  touched.value[field] = true;
  validateField(field);
};

const validate = () => {
  for (const field of Object.keys(touched.value) as Array<keyof typeof touched.value>) {
    touched.value[field] = true;
    validateField(field);
  }
};

const isFormValid = computed(() => {
  return Object.values(errors.value).every((error) => error === "");
});

defineExpose({
  validate,
  isFormValid,
  errors,
  touched,
});
</script>

<template>
  <div class="invoice-details">
    <div class="row -mx-4">
      <!-- Invoice Name -->
      <div class="w-full lg:w-1/2 px-4 mt-4">
        <label for="invoice-name" class="field-label">{{ t('form.invoice_name') }}*</label>
        <input
          id="invoice-name"
          type="text"
          v-model="invoice.name"
          @input="handleInput('name')"
          @blur="handleBlur('name')"
          class="field-input rounded-md"
        />
        <p v-if="touched.name && errors.name" class="mt-2 text-sm text-red-600">{{ errors.name }}</p>
      </div>
      <!-- Invoice Email -->
      <div class="w-full lg:w-1/2 px-4 mt-4">
        <label for="invoice-email" class="field-label">{{ t('form.invoice_email') }}*</label>
        <input
          id="invoice-email"
          type="email"
          v-model="invoice.email"
          @input="handleInput('email')"
          @blur="handleBlur('email')"
          class="field-input rounded-md"
        />
        <p v-if="touched.email && errors.email" class="mt-2 text-sm text-red-600">{{ errors.email }}</p>
      </div>
    </div>

    <div class="row -mx-4">
      <!-- Invoice Street -->
      <div class="w-full lg:w-1/2 px-4 mt-4">
        <label for="invoice-street" class="field-label">{{ t('form.invoice_street') }}</label>
        <input
          id="invoice-street"
          type="text"
          v-model="invoice.street"
          @input="handleInput('street')"
          @blur="handleBlur('street')"
          class="field-input rounded-md"
        />
        <p v-if="touched.street && errors.street" class="mt-2 text-sm text-red-600">{{ errors.street }}</p>
      </div>
      <!-- Invoice DIC -->
      <div class="w-full lg:w-1/2 px-4 mt-4">
        <label for="invoice-dic" class="field-label">{{ t('form.invoice_tax_id') }}</label>
        <input
          id="invoice-dic"
          type="text"
          v-model="invoice.dic"
          @input="handleInput('dic')"
          @blur="handleBlur('dic')"
          class="field-input rounded-md"
        />
        <p v-if="touched.dic && errors.dic" class="mt-2 text-sm text-red-600">{{ errors.dic }}</p>
      </div>
    </div>

    <div class="row -mx-4">
      <!-- Invoice City -->
      <div class="w-full lg:w-1/2 px-4 mt-4">
        <label for="invoice-city" class="field-label">{{ t('form.invoice_city') }}</label>
        <input
          id="invoice-city"
          type="text"
          v-model="invoice.city"
          @input="handleInput('city')"
          @blur="handleBlur('city')"
          class="field-input rounded-md"
        />
        <p v-if="touched.city && errors.city" class="mt-2 text-sm text-red-600">{{ errors.city }}</p>
      </div>
      <!-- Invoice IC -->
      <div class="w-full lg:w-1/2 px-4 mt-4">
        <label for="invoice-ic" class="field-label">{{ t('form.invoice_vat') }}</label>
        <input
          id="invoice-ic"
          type="text"
          v-model="invoice.ic"
          @input="handleInput('ic')"
          @blur="handleBlur('ic')"
          class="field-input rounded-md"
        />
        <p v-if="touched.ic && errors.ic" class="mt-2 text-sm text-red-600">{{ errors.ic }}</p>
      </div>
    </div>

    <div class="row -mx-4">
      <!-- Invoice ZIP -->
      <div class="w-full lg:w-1/2 px-4 mt-4">
        <label for="invoice-zip" class="field-label">{{ t('form.invoice_zip') }}</label>
        <input
          id="invoice-zip"
          type="text"
          v-model="invoice.zip"
          @input="handleInput('zip')"
          @blur="handleBlur('zip')"
          class="field-input rounded-md"
        />
        <p v-if="touched.zip && errors.zip" class="mt-2 text-sm text-red-600">{{ errors.zip }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.field-label {
  @apply block text-sm font-medium mb-2 text-gray-300;
}

.field-input {
  @apply mt-1 block w-full p-2 border border-gray-300 text-black focus:outline-none focus:ring-0 rounded-md;
}
</style>
