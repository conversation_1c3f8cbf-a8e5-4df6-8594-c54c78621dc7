<script setup lang="ts">
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { z } from "zod";

const { t } = useI18n();

const props = defineProps<{
	modelValue: {
		firstname: string;
		lastname: string;
		email: string;
		phone: string;
	};
	dataRequired: boolean;
	phoneRequired: boolean;
}>();

const emit =
	defineEmits<
		(
			e: "update:modelValue",
			value: typeof props.modelValue
		) => void
	>();

const validate = () => {
	// Set all fields as touched
	for (const field of Object.keys(touched.value) as Array<keyof typeof person.value>) {
    touched.value[field] = true;
    validateField(field);
	}
};

const person = computed({
	get: () => props.modelValue,
	set: (value) => emit("update:modelValue", value),
});

// Define the schema using Zod
const schema = computed(() =>
	z.object({
		firstname: props.dataRequired
			? z.string().min(1, { message: t("validation.firstname") })
			: z
					.string()
					.min(1, { message: t("validation.firstname") })
					.optional()
					.or(z.literal("")),
		lastname: props.dataRequired
			? z.string().min(1, { message: t("validation.lastname") })
			: z
					.string()
					.min(1, { message: t("validation.lastname") })
					.optional()
					.or(z.literal("")),
		email: props.dataRequired
			? z.string().email({ message: t("validation.email") })
			: z
					.string()
					.email({ message: t("validation.email") })
					.optional()
					.or(z.literal("")),
		phone:
			props.phoneRequired && props.dataRequired
				? z
						.string()
						.regex(/^\+?[0-9]{9,15}$/, { message: t("validation.phone") })
				: z
						.string()
						.regex(/^\+?[0-9]{9,15}$/, { message: t("validation.phone") })
						.optional()
						.or(z.literal("")),
	}),
);

const errors = ref({
	firstname: "",
	lastname: "",
	email: "",
	phone: "",
});

const touched = ref({
	firstname: false,
	lastname: false,
	email: false,
	phone: false,
});

const validateField = (field: keyof typeof person.value) => {
	if (!touched.value[field]) return;
	const fieldSchema = schema.value.shape[field];
	const validation = fieldSchema.safeParse(person.value[field]);
	if (!validation.success) {
		errors.value[field] = validation.error.errors[0].message;
	} else {
		errors.value[field] = "";
	}
};

const handleInput = (field: keyof typeof person.value) => {
	touched.value[field] = true;
	validateField(field);
};

const handleBlur = (field: keyof typeof person.value) => {
	touched.value[field] = true;
	validateField(field);
};

const isFormValid = computed(() => {
	return Object.values(errors.value).every((error) => error === "");
});

defineExpose({
	validate,
	isFormValid,
	errors,
	touched,
});
</script>

<template>
  <div class="person-details">
    <div class="mb-4 row -mx-4">
      <div class="w-full sm:w-1/2 lg:w-1/4 px-4">
        <label for="firstname" class="field-label">{{ t('form.firstname') }}{{ props.dataRequired ? '*' : '' }}</label>
        <input id="firstname" type="text" v-model="person.firstname" @input="handleInput('firstname')"
          @blur="handleBlur('firstname')" class="field-input" />
        <p v-if="touched.firstname && errors.firstname" class="mt-2 text-sm text-red-600">{{ errors.firstname }}</p>
      </div>
      <div class="w-full sm:w-1/2 lg:w-1/4 px-4">
        <label for="lastname" class="field-label">{{ t('form.lastname') }}{{ props.dataRequired ? '*' : '' }}</label>
        <input id="lastname" type="text" v-model="person.lastname" @input="handleInput('lastname')"
          @blur="handleBlur('lastname')" class="field-input" />
        <p v-if="touched.lastname && errors.lastname" class="mt-2 text-sm text-red-600">{{ errors.lastname }}</p>
      </div>
      <div class="w-full sm:w-1/2 lg:w-1/4 px-4">
        <label for="email" class="field-label">{{ t('form.email') }}{{ dataRequired ? '*' : '' }}</label>
        <input id="email" type="email" v-model="person.email" @input="handleInput('email')"
          @blur="handleBlur('email')" class="field-input" />
        <p v-if="touched.email && errors.email" class="mt-2 text-sm text-red-600">{{ errors.email }}</p>
      </div>
 
      <div class="w-full sm:w-1/2 lg:w-1/4 px-4">
        <label for="phone" class="field-label">{{ t('form.phone') }}{{ phoneRequired && dataRequired ? '*' : '' }}</label>
        <input id="phone" type="tel" v-model="person.phone" @input="handleInput('phone')"
          @blur="handleBlur('phone')" class="field-input" />
        <p v-if="touched.phone && errors.phone" class="mt-2 text-sm text-red-600">{{ errors.phone }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.field-label {
  @apply block text-sm font-medium mb-2 text-gray-300;
}
.field-input {
  @apply mt-1 block w-full p-2 border border-gray-300 text-black focus:outline-none focus:ring-0 rounded-md;
}
</style>