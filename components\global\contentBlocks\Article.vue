<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { locale } = useI18n();

const props = defineProps({
	content: {
		type: String,
		required: true,
	},
	meta: {
		type: Object,
		required: false,
	},
});

function getAuthorSubtitlePart() {
	if (props.meta?.showAuthor && props.meta?.author) {
		return `${props.meta?.author?.firstname} ${props.meta?.author?.lastname} |`;
	}
	return '';
}

function getArticleSubtitle() {
	// publishDataInCurrentLocale | authorNameAndSurnameIfShowAuthorIsTrue | categoryName
	const publishDate = new Date(props.meta?.publishDate).toLocaleDateString(locale.value);
	
	return `${publishDate} | ${getAuthorSubtitlePart()} ${props.meta?.category}`;
}

</script>

<template>
	<div v-if="meta?.hasFullHtmlControl" v-html="props.content" v-auto-id></div>
	<div v-else class="content">
		<div class="content-container">
			<div class="row">
				<div class="px-10 py-5 card">
					<h1>{{ props.meta?.title }}</h1>
					<div class="article-subtitle">{{ getArticleSubtitle() }}</div>
					<div v-html="props.meta?.anotation" v-auto-id></div>
					<div v-html="props.content" v-auto-id></div>
				</div>
			</div>
		</div>
	</div>
</template>

<style scoped>
.article-subtitle {
	@apply text-tda-400 mb-6;
}
</style>