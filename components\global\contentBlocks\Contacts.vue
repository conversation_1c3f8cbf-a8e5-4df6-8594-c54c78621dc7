<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const target = "contacts";
const { locale } = useI18n();

const { data } = useFetch<WebContactGroupEntityResponseCollection>(
	`/api/${target}?locale=${locale.value}`,
);

const orderedContactGroups = computed(() => {
	if (!data.value) return [];
	const groups = data.value.data.sort(
		(a, b) => (a.attributes?.weight ?? 0) - (b.attributes?.weight ?? 0),
	);
	// sort each group's contacts by weight
	for (const group of groups) {
		if (group.attributes?.webContacts) {
			group.attributes.webContacts.data =
				group.attributes.webContacts.data.sort(
					(a, b) => (a.attributes?.weight ?? 0) - (b.attributes?.weight ?? 0),
				);
		}
	}
	return groups;
});

const props = defineProps({
	content: {
		type: String,
		required: true,
	},
	meta: {
		type: Object,
		required: false,
	},
});
</script>

<template>
  <div v-html="props.content" v-auto-id></div>

  <div class="pb-6" :class="{ 'dark-content': i === 0, 'pt-8': i === 1 }" v-for="(contactGroup, i) in orderedContactGroups"
    :key="contactGroup.id ?? ''">
    <div class="content-container">
      <div class="row" v-auto-id>
        <h3 class="w-full mt-1 ml-4">{{ contactGroup.attributes?.name }}</h3>
        <div class="w-full flex flex-wrap mt-4 -mr-4">
          <div class="w-full lg:w-6/12 px-4 mb-6"
            v-for="(contact, j) in contactGroup.attributes?.webContacts?.data" :key="j">
            <ContactCard :contact="contact" :showCard="i !== 0" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>