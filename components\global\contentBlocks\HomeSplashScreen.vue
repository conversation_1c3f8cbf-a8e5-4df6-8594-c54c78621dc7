<script setup lang="ts">
import type { UploadFileEntity } from "@/models/types";
import { createImageUrls } from "@/utils/dataUtils";
import { computed } from "vue";

const props = defineProps({
	content: {
		type: String,
		required: true,
	},
	meta: {
		type: Object,
		required: true,
	},
});

const picture = ref(props.meta?.picture as UploadFileEntityResponse);
const imageUrls = computed(() =>
	createImageUrls(picture.value.data as UploadFileEntity),
);
</script>

<template>
  <div class="relative min-h-screen -top-20">
    <!-- Using srcset and sizes for responsive images -->
    <img :src="imageUrls.largeImageUrl" :srcset="`${imageUrls.smallImageUrl} 480w, ${imageUrls.mediumImageUrl} 800w, ${imageUrls.largeImageUrl} 1200w`"
         sizes="(max-width: 600px) 480px, (max-width: 1024px) 800px, 1200px" alt="Tour de App" loading="lazy"
         class="absolute inset-0 w-full h-full object-cover align-middle">
    <div class="absolute inset-0 flex w-full h-full md:items-center pb-20 pt-32 bg-gradient-to-r from-tda-300/70 to-background/85" 
      v-html="content" v-auto-id>
    </div>
  </div>
</template>
