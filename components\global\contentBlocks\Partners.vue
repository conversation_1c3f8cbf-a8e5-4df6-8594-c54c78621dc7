<script setup lang="ts">
import type { FBQueryParams } from "@/models/FBQueryParams";
import { useFishBushStore } from "@/stores/FishBush.js";
import { useI18n } from 'vue-i18n';

const FBstore = useFishBushStore();
const resource = "web-partner-groups";
const { locale } = useI18n();

const modifiers: FBQueryParams = {
	fields: ["name", "weight", "importance"],
	populate: {
		webPartners: {
			fields: ["name", "url", "text", "weight"],
			populate: ["logo"],
		},
	},
	locale: locale.value
};

const { data: partnersData } = await useAsyncData(resource, async () => {
	if (!FBstore.getFBdata(resource, modifiers)) {
		await FBstore.fetchFishBushData(resource, modifiers);
	}

	return FBstore.getFBdata(resource, modifiers);
});

// compute the width class based on the importance high -> w-4/12, medium -> w-3/12, low -> 1/5

const partners = computed(() => {
	// first, sort the partner groups by weight
	const sortedGroups = partnersData.value?.data.sort(
		(a, b) => (a.attributes?.weight ?? 0) - (b.attributes?.weight ?? 0),
	) as WebPartnerGroupEntity[];

	/* then, split the partner groups into array (packs):
    - if the group has more than one item, start a pack with type 'multiples'
    - 'multiples' packs have one name for the entire pack
    - if the group has only one item and the previous pack is not of type 'singles', start a pack with type 'singles'
    - if the group has only one item and the previous pack is of type 'singles' and the previous group in the pack has the same importance, add the group to the pack
    - if the group has only one item and the previous pack is of type 'singles' and the previous group in the pack has a different importance, start a new pack with type 'singles'
    - 'singles' packs have individual names for each item
  */
	type ExtendedWebPartnerEntity = WebPartnerEntity & { name?: string };

	type Pack = {
		type: "singles" | "multiples";
		name?: string;
		partners: ExtendedWebPartnerEntity[];
		packID: string;
		importance: "high" | "medium" | "low";
	};

	const packs = sortedGroups.reduce<Pack[]>((acc, group, index) => {
		const previousGroup = sortedGroups[index - 1];
		const previousPack = acc[acc.length - 1];
		const previousGroupImportance = previousGroup?.attributes?.importance;

		// if some of the mandatory data is missing, skip the group
		if (!group.attributes) return acc;
		if (!group.attributes.webPartners) return acc;

		const groupImportance = group.attributes.importance;
		const groupName = group.attributes.name;
		const groupPartnerCount = group.attributes.webPartners.data.length;

		if (groupPartnerCount > 1) {
			acc.push({
				// start a new pack with type 'multiples'
				type: "multiples",
				name: groupName,
				partners: group.attributes.webPartners.data,
				packID: group.id as string,
				importance: groupImportance,
			});
		} else {
			const singleToPush: ExtendedWebPartnerEntity =
				group.attributes.webPartners.data[0];
			if (!singleToPush) {
				//TODO: skip the group if the partner is missing
				return acc;
			}
			singleToPush.name = groupName;
			// either start a new pack or add the group to the previous pack
			if (
				!previousPack ||
				groupImportance !== previousGroupImportance ||
				previousPack.type === "multiples"
			) {
				acc.push({
					// start a new pack with type 'singles'
					type: "singles",
					partners: [singleToPush],
					packID: group.id as string,
					importance: groupImportance,
				});
			} else {
				previousPack.partners.push(singleToPush);
			}
		}

		return acc;
	}, []);
	// sort each pack of multiple partners by weight
	for (const pack of packs) {
		if (pack.type === "multiples") {
			pack.partners = pack.partners.sort(
				(a, b) => (a.attributes?.weight ?? 0) - (b.attributes?.weight ?? 0),
			);
		}
	}
	return packs;
});

const props = defineProps({
	content: {
		type: String,
		required: true,
	},
	meta: {
		type: Object,
		required: false,
	},
});
</script>

<template>
  <div v-html="props.content" v-auto-id></div>

  <div class="content" v-if="partners.length">
    <div class="content-container">
      <div v-for="partnerPack in partners" :key="partnerPack.packID" class="mt-10 first:mt-0">
        <!-- if multiples -->
        <div v-if="partnerPack.type === 'multiples' && partnerPack.partners.length">
          <div class="row justify-center">
            <div class="text-center text-tda-300 text-lg font-bold w-full">
              {{ partnerPack.name }}
            </div>
          </div>
          <div class="row justify-center">
            <LazyPartnerCard v-for="partner in partnerPack.partners" :key="partner.attributes?.name"
              :partnerDetails="{details: partner.attributes}"
              :importance="partnerPack.importance" />
          </div>
        </div>
        <!-- if singles -->
        <div v-else class="row justify-center">
          <LazyPartnerCard v-for="single in partnerPack.partners" :key="single.name"
          :partnerDetails="{details: single.attributes}" :groupName="single.name"
            :importance="partnerPack.importance" />
        </div>
      </div>
    </div>
  </div>
</template>