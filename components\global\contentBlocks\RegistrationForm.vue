<script setup lang="ts">
import { stringify } from "qs";
import { useI18n } from "vue-i18n";
import { z } from "zod";

const { t, locale } = useI18n();

const router = useRouter();
const props = defineProps({
	content: {
		type: String,
		required: true,
	},
	meta: {
		type: Object,
		required: false,
	},
});

watch(
	() => locale.value,
	() => {
		// Re-validate the form to update error messages
		validateForm();
	},
);

const isSending = ref(false);
const target = "schools";
const querySchoolTypes = {
	q: props.meta?.schoolTypes.data.map(
		(schoolType: { id: string }) => schoolType.id,
	),
};
const queryString = stringify(querySchoolTypes, { encodeValuesOnly: true });
const { data: schoolData } = useFetch<Array<SchoolEntity>>(
	`/api/${target}?${queryString}`,
	{ server: false },
);

onMounted(() => {
	// if there is a referral code in the URL, set it as the default value
	const urlParams = new URLSearchParams(window.location.search);
	const urlRefCode = urlParams.get("ref");
	if (urlRefCode) {
		referralCode.value = urlRefCode;
	}
});

const formMetaData = computed(() => {
	const meta = props.meta as ComponentContentBlocksRegistrationForm;
	return {
		...meta,
		checkFields: (meta.checkFields ?? []).map((field) => {
			const content = field?.content || "";
			const lastLtIndex = content.lastIndexOf("<");

			// If the field is required and the '<' character is found, insert '*' before it
			const newContent =
				lastLtIndex !== -1 && field?.isRequired
					? `${content.substring(0, lastLtIndex)}*${content.substring(lastLtIndex)}`
					: content;

			return { ...field, content: newContent };
		}),
	};
});

// Define the schema using Zod
const schema = computed(() => {
	const _ = locale.value;
	return z.object({
		teamName: z
			.string()
			.min(1, { message: t("validation.team_name") })
			.max(255, { message: t("validation.team_name_max") }),
		school: z
			.object(
				{
					id: z.any(),
					attributes: z.object({
						name: z.string(),
					}),
				},
				{ message: t("validation.school") },
			)
			.refine((school) => schoolData.value?.some((s) => s.id === school.id), {
				message: t("validation.school"),
			}),
		referralCode: z
			.string()
			.refine(
				(code) => code === "" || (code.length >= 10 && code.length <= 16),
				{
					message: t("validation.referral"),
				},
			),
			invoice: props.meta?.hasInvoice
      ? z.object({
          name: z.string().min(1, { message: t("validation.invoice_name") }),
          email: z.string().email({ message: t("validation.invoice_email") }),
          street: z.string().min(1, { message: t("validation.invoice_street") }),
          city: z.string().min(1, { message: t("validation.invoice_city") }),
          zip: z.string().min(1, { message: t("validation.invoice_zip") }),
          ic: z.string().optional(),
          dic: z.string().optional(),
        })
      : z.any(),
	});
});

// In your parent component's script
interface FormComponentInstance {
	validate: () => void;
	isFormValid: boolean;
	errors: Record<string, string>;
	touched: Record<string, boolean>;
}

// Reactive form state
const teamName = ref("");
const school = ref<SchoolEntity | null>(null);
const referralCode = ref("");

const invoice = ref({
  name: "",
  email: "",
  street: "",
  city: "",
  zip: "",
  ic: "",
  dic: "",
});

// const schools = schoolData.value?.filter(school => school.attributes?.name) || [];

const schools = computed(
	() => schoolData.value?.filter((school) => school.attributes?.name) || [],
);

const teacher = ref({
	firstname: "",
	lastname: "",
	email: "",
	phone: "",
});

const teacherRef = ref<FormComponentInstance | null>(null);
const contestantRefs = ref<(FormComponentInstance | null)[]>([]);
const checkboxRefs = ref<(HTMLInputElement | null)[]>([]);
const invoiceRef = ref<FormComponentInstance | null>(null);

// instead, let's use a ref with initial calculated length -> props.meta?.maxContestants + props.meta?.substituteContestants
const contestants = ref(
	Array.from(
		{ length: props.meta?.maxContestants + props.meta?.substituteContestants },
		() => ({
			firstname: "",
			lastname: "",
			email: "",
			phone: "",
		}),
	),
);

// Define the errors object with string keys and string values
const errors = ref<Record<string, string>>({
	teamName: "",
	school: "",
	referralCode: "",
});

// Track touched fields
const touched = ref<Record<string, boolean>>({
	teamName: false,
	school: false,
	referralCode: false,
});

// Lazy mode validation function
const validateForm = () => {
	const validation = schema.value.safeParse({
		teamName: teamName.value,
		school: school.value,
		referralCode: referralCode.value,
		invoice: invoice.value,
	});

	// Clear previous errors
	for (const key of Object.keys(errors.value)) {
		if (touched.value[key as keyof typeof touched.value]) {
			errors.value[key as keyof typeof errors.value] = "";
		}
	}

	// Handle validation errors
	if (!validation.success) {
		for (const error of validation.error.errors) {
			if (error.path[0] && typeof error.path[0] === "string") {
				if (touched.value[error.path[0]]) {
					errors.value[error.path[0]] = error.message;
				}
			}
		}
	}

  // Validate the invoice details if applicable and if any invoice fields have been touched
  if (props.meta?.hasInvoice && invoiceRef.value) {
    const invoiceTouched = Object.values(invoiceRef.value.touched).some((t) => t);
    if (invoiceTouched) {
      invoiceRef.value.validate();
    }
  }

  // Validate the teacher details if applicable
  if (props.meta?.hasTeacher && teacherRef.value) {
    const teacherTouched = Object.values(teacherRef.value.touched).some((t) => t);
    if (teacherTouched) {
      teacherRef.value.validate();
    }
  }

  // Validate each contestant
  for (const contestantRef of contestantRefs.value) {
    if (contestantRef) {
      const contestantTouched = Object.values(contestantRef.touched).some((t) => t);
      if (contestantTouched) {
        contestantRef.validate();
      }
    }
  }
};

watch(teacher.value, (newTeacher) => {
  if (props.meta?.hasInvoice) {
    invoice.value.email = newTeacher?.email || "";
  }
});

watch(school, (newSchool) => {
  if (props.meta?.hasInvoice) {
    invoice.value.name = newSchool?.attributes?.shortName || "";
    invoice.value.street = newSchool?.attributes?.street || "";
    invoice.value.city = newSchool?.attributes?.town || "";
    invoice.value.zip = newSchool?.attributes?.postCode?.toString() || "";
    invoice.value.ic = newSchool?.attributes?.cin || "";
  }
});

// Handle field blur
const handleBlur = (field: keyof typeof touched.value) => {
	touched.value[field] = true;
	validateForm();
};

const submitForm = async () => {
	forceTouch();
	validateForm();
	// checkboxRefs
	for (const ref of checkboxRefs.value) {
		ref?.checkValidity();
	}

	const checkboxesValid = checkboxRefs.value.every(
		// must be either checked or not required
		(ref) => ref?.checked || !ref?.required,
	);

	// find checkbox with the id partner-disclosure
	const partnerDisclosureCheckbox = checkboxRefs.value.find(
		(ref) => ref?.id === "partner-disclosure",
	);

	if (checkboxesValid && isFormValid.value) {
		// send a POST request to /api/register
		const data = {
			teamName: teamName.value,
			school: school.value,
			referralCode: referralCode.value,
			teacher: teacher.value,
			contestants: contestants.value,
			contestantCount: contestants.value.length,
			hasCaptain: props.meta?.hasCaptain,
			allowedPartnerDataAccess: partnerDisclosureCheckbox?.checked,
			invoice: props.meta?.hasInvoice ? invoice.value : undefined,
		};

		isSending.value = true;

		$fetch("/api/register", {
			method: "POST",
			body: data,
		})
			.then((response) => {
				const registrationSuccessUrl =
					formMetaData.value.registrationSuccessUrl || "/success";
				switch (response.statusCode) {
					case 200:
						router.push(
							`${registrationSuccessUrl}?referralCode=${response.referralCode}`,
						);
						break;
					case 400:
						for (const duplicateEmail of response.duplicateContestants) {
							for (const [index, ref] of contestantRefs.value.entries()) {
								if (ref && contestants.value[index].email === duplicateEmail) {
									// Set error on the email field
									ref.errors.email = "Duplicitní email, použijte jiný";
									ref.touched.email = true; // Mark as touched
								}
							}
						}
						break;
					default:
						break;
				}
			})
			.catch((error) => {
				console.error(error);
			})
			.finally(() => {
				isSending.value = false;
			});
	}
};

// Set all fields as touched when the form is submitted
const forceTouch = () => {
	for (const key of Object.keys(touched.value)) {
		touched.value[key as keyof typeof touched.value] = true;
	}
	// touch invoice
	if (props.meta?.hasInvoice && invoiceRef.value) {
    invoiceRef.value.validate();
  }
	// touch teacher and contestants
	if (props.meta?.hasTeacher && teacherRef.value) {
		teacherRef.value.validate();
	}
	for (const contestantRef of contestantRefs.value) {
		if (contestantRef) {
			contestantRef.validate();
		}
	}
};

// Computed property to enable/disable submit button
const isFormValid = computed(() => {
	const formFieldsValid =
		!errors.value.teamName &&
		!errors.value.school &&
		!errors.value.referralCode;
	let teacherValid = true;
	if (props.meta?.hasTeacher && teacherRef.value) {
		teacherValid = teacherRef.value.isFormValid;
	}
	let invoiceValid = true;
  if (props.meta?.hasInvoice && invoiceRef.value) {
    invoiceValid = invoiceRef.value.isFormValid;
  }

	const contestantsValid = contestantRefs.value.every(
		(ref) => ref?.isFormValid,
	);

	return formFieldsValid && invoiceValid && teacherValid && contestantsValid;
});

// Function to update contestant details
const updateContestant = (
	index: number,
	updatedContestant: {
		firstname: string;
		lastname: string;
		email: string;
		phone: string;
	},
) => {
	contestants.value[index] = updatedContestant;
};
</script>

<template>
  <div v-html="props.content" v-auto-id></div>

  <div class="content">
    <div class="content-container-thin">
      <form @submit.prevent="validateForm">
        <div class="px-10 py-5 my-5 card">
          <h3>{{ t('form.team') }}</h3>
          <!-- Název týmu input -->
          <div class="mb-4">
            <label for="teamName" class="field-label">{{ t('form.team_name') }}*</label>
            <input id="teamName" type="text" v-model="teamName" @blur="handleBlur('teamName')"
              class="field-input rounded-md" autocomplete="off" />
            <p v-if="touched.teamName && errors.teamName" class="mt-2 text-sm text-red-600">{{ errors.teamName }}</p>
          </div>

          <!-- Škola select -->
          <div class="mb-4">
            <label for="school" class="field-label">{{ t('form.school') }}*</label>
            <FormSearchDropdown v-model="school" :schools="schools" :error="touched.school ? errors.school : ''"
              @blur="handleBlur('school')" />
          </div>

          <!-- Kód doporučení input -->
          <div class="mb-4" v-if="formMetaData.hasReferral">
            <label for="referralCode" class="field-label">{{ t('form.referral_code') }}</label>
            <input id="referralCode" type="text" v-model="referralCode" @blur="handleBlur('referralCode')"
              class="field-input rounded-md" autocomplete="off" />
            <p v-if="touched.referralCode && errors.referralCode" class="mt-2 text-sm text-red-600">{{
              errors.referralCode }}</p>
          </div>
        </div>

        <div class="px-10 py-5 my-5 card" v-if="props.meta?.hasTeacher">
          <h3>{{ t('form.teacher') }}</h3>
          <FormPersonDetails ref="teacherRef" v-model="teacher" :phone-required="true" :data-required="true" />
        </div>

        <div class="px-10 py-5 my-5 card">
          <h3>{{ t('form.contestants') }}</h3>
          <div v-for="(contestant, index) in contestants" :key="index" class="mb-6">
            <div v-if="index === 0 && props.meta?.hasCaptain" class="text-2xl my-2">{{ t('form.captain') }}</div>
            <div v-else-if="(index === 0 && !props.meta?.hasCaptain) || (index === 1 && props.meta?.hasCaptain)"
              class="text-2xl my-2">{{ t('form.members') }}</div>
            <div v-else-if="index === props.meta?.maxContestants" class="text-2xl my-2">{{ t('form.substitutes') }}</div>
            <FormPersonDetails :model-value="contestant" @update:model-value="updateContestant(index, $event)"
              :phone-required="index === 0 && props.meta?.hasCaptain"
              :data-required="index < props.meta?.minContestants" ref="contestantRefs" />
          </div>
        </div>

				
				<div class="px-10 py-5 my-5 card" v-if="formMetaData.hasInvoice">
					<h3>{{ t('form.invoice_header') }}</h3>
					<FormInvoiceDetails v-model="invoice" ref="invoiceRef" />
				</div>


        <div class="px-10 py-5 my-5 card">
          <div class="submit-confirm-group text-justify">
            <div class="flex items-center mb-4" v-for="checkItem in formMetaData.checkFields">
              <input type="checkbox" :id="checkItem?.name" :name="checkItem?.name" :required="checkItem?.isRequired"
                class="w-5 h-5 text-tda rounded focus:ring-tda/50"
                oninvalid="this.setCustomValidity(t('checkbox'))" onchange="this.setCustomValidity('')"
                ref="checkboxRefs">
              <label :for="checkItem?.name" class="ml-3" v-html="checkItem?.content">
              </label>
            </div>

            <button type="submit" @click="submitForm" :disabled="!isFormValid || isSending"
              class="rounded-md btn btn-primary py-3 px-6">
              {{ t('form.submit') }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <LoadingSpinner v-if="isSending" />
</template>

<style scoped>
.field-label {
  @apply block text-sm font-medium mb-2 text-gray-300;
}

.field-input {
  @apply mt-1 block w-full p-2 border border-gray-300 text-black focus:outline-none focus:ring-0
}
</style>