<script setup lang="ts">
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
	content: {
		type: String,
		required: true,
	},
	meta: {
		type: Object,
		required: false,
	},
});

const link = (props.meta as ComponentContentBlocksRegistrationSuccess)
	.refNextUrl;
const route = useRoute();

// computed referrer from the URL
const refCode = computed(() => {
	return route.query.referralCode?.toString() || null;
});

const refLink = computed(() => {
	if (!refCode.value) {
		return link;
	}
	return `${link}?ref=${refCode.value}`;
});
</script>

<template>
  <div v-html="props.content" v-auto-id></div>
  <div class="content">
    <div class="content-container">
      <div class="row">
        <div class="px-10 py-5 card">
          <h1>{{ t('registration_success') }}</h1>
          <p class="text-lg">
            {{ t('registration_share') }}
          </p>
          <p v-if="refCode" class="text-lg">
            {{ t('registration_referral') }}
            <a :href="`${link}?ref=${refCode}`"><span class="font-bold">{{ refCode }}</span></a> {{ t('registration_referral_save') }}.
          </p>
          <div class="w-full my-3 flex items-center justify-center">
            <a class="btn btn-lg btn-primary" :href="refLink?.toString()">{{ t('registration_next') }}</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>