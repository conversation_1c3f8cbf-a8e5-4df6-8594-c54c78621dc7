export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: any;
  /** A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar. */
  DateTime: any;
  /** A date string, such as 2007-12-03, compliant with the `full-date` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar. */
  Date: any;
  /** The `BigInt` scalar type represents non-fractional signed whole numeric values. */
  Long: any;
  /** The `Upload` scalar type represents a file upload. */
  Upload: any;
  TdaFlagNoteDynamicZoneInput: any;
  WebPageBlocksDynamicZoneInput: any;
  /** A string used to identify an i18n locale */
  I18NLocaleCode: any;
};

export type Error = {
  __typename?: 'Error';
  code: Scalars['String'];
  message?: Maybe<Scalars['String']>;
};

export type Pagination = {
  __typename?: 'Pagination';
  total: Scalars['Int'];
  page: Scalars['Int'];
  pageSize: Scalars['Int'];
  pageCount: Scalars['Int'];
};

export type ResponseCollectionMeta = {
  __typename?: 'ResponseCollectionMeta';
  pagination: Pagination;
};

export enum PublicationState {
  Live = 'LIVE',
  Preview = 'PREVIEW'
}

export type IdFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  not?: InputMaybe<IdFilterInput>;
  eq?: InputMaybe<Scalars['ID']>;
  eqi?: InputMaybe<Scalars['ID']>;
  ne?: InputMaybe<Scalars['ID']>;
  nei?: InputMaybe<Scalars['ID']>;
  startsWith?: InputMaybe<Scalars['ID']>;
  endsWith?: InputMaybe<Scalars['ID']>;
  contains?: InputMaybe<Scalars['ID']>;
  notContains?: InputMaybe<Scalars['ID']>;
  containsi?: InputMaybe<Scalars['ID']>;
  notContainsi?: InputMaybe<Scalars['ID']>;
  gt?: InputMaybe<Scalars['ID']>;
  gte?: InputMaybe<Scalars['ID']>;
  lt?: InputMaybe<Scalars['ID']>;
  lte?: InputMaybe<Scalars['ID']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type BooleanFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
  not?: InputMaybe<BooleanFilterInput>;
  eq?: InputMaybe<Scalars['Boolean']>;
  eqi?: InputMaybe<Scalars['Boolean']>;
  ne?: InputMaybe<Scalars['Boolean']>;
  nei?: InputMaybe<Scalars['Boolean']>;
  startsWith?: InputMaybe<Scalars['Boolean']>;
  endsWith?: InputMaybe<Scalars['Boolean']>;
  contains?: InputMaybe<Scalars['Boolean']>;
  notContains?: InputMaybe<Scalars['Boolean']>;
  containsi?: InputMaybe<Scalars['Boolean']>;
  notContainsi?: InputMaybe<Scalars['Boolean']>;
  gt?: InputMaybe<Scalars['Boolean']>;
  gte?: InputMaybe<Scalars['Boolean']>;
  lt?: InputMaybe<Scalars['Boolean']>;
  lte?: InputMaybe<Scalars['Boolean']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
};

export type StringFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  not?: InputMaybe<StringFilterInput>;
  eq?: InputMaybe<Scalars['String']>;
  eqi?: InputMaybe<Scalars['String']>;
  ne?: InputMaybe<Scalars['String']>;
  nei?: InputMaybe<Scalars['String']>;
  startsWith?: InputMaybe<Scalars['String']>;
  endsWith?: InputMaybe<Scalars['String']>;
  contains?: InputMaybe<Scalars['String']>;
  notContains?: InputMaybe<Scalars['String']>;
  containsi?: InputMaybe<Scalars['String']>;
  notContainsi?: InputMaybe<Scalars['String']>;
  gt?: InputMaybe<Scalars['String']>;
  gte?: InputMaybe<Scalars['String']>;
  lt?: InputMaybe<Scalars['String']>;
  lte?: InputMaybe<Scalars['String']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type IntFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  not?: InputMaybe<IntFilterInput>;
  eq?: InputMaybe<Scalars['Int']>;
  eqi?: InputMaybe<Scalars['Int']>;
  ne?: InputMaybe<Scalars['Int']>;
  nei?: InputMaybe<Scalars['Int']>;
  startsWith?: InputMaybe<Scalars['Int']>;
  endsWith?: InputMaybe<Scalars['Int']>;
  contains?: InputMaybe<Scalars['Int']>;
  notContains?: InputMaybe<Scalars['Int']>;
  containsi?: InputMaybe<Scalars['Int']>;
  notContainsi?: InputMaybe<Scalars['Int']>;
  gt?: InputMaybe<Scalars['Int']>;
  gte?: InputMaybe<Scalars['Int']>;
  lt?: InputMaybe<Scalars['Int']>;
  lte?: InputMaybe<Scalars['Int']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
};

export type LongFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
  not?: InputMaybe<LongFilterInput>;
  eq?: InputMaybe<Scalars['Long']>;
  eqi?: InputMaybe<Scalars['Long']>;
  ne?: InputMaybe<Scalars['Long']>;
  nei?: InputMaybe<Scalars['Long']>;
  startsWith?: InputMaybe<Scalars['Long']>;
  endsWith?: InputMaybe<Scalars['Long']>;
  contains?: InputMaybe<Scalars['Long']>;
  notContains?: InputMaybe<Scalars['Long']>;
  containsi?: InputMaybe<Scalars['Long']>;
  notContainsi?: InputMaybe<Scalars['Long']>;
  gt?: InputMaybe<Scalars['Long']>;
  gte?: InputMaybe<Scalars['Long']>;
  lt?: InputMaybe<Scalars['Long']>;
  lte?: InputMaybe<Scalars['Long']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
};

export type FloatFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
  not?: InputMaybe<FloatFilterInput>;
  eq?: InputMaybe<Scalars['Float']>;
  eqi?: InputMaybe<Scalars['Float']>;
  ne?: InputMaybe<Scalars['Float']>;
  nei?: InputMaybe<Scalars['Float']>;
  startsWith?: InputMaybe<Scalars['Float']>;
  endsWith?: InputMaybe<Scalars['Float']>;
  contains?: InputMaybe<Scalars['Float']>;
  notContains?: InputMaybe<Scalars['Float']>;
  containsi?: InputMaybe<Scalars['Float']>;
  notContainsi?: InputMaybe<Scalars['Float']>;
  gt?: InputMaybe<Scalars['Float']>;
  gte?: InputMaybe<Scalars['Float']>;
  lt?: InputMaybe<Scalars['Float']>;
  lte?: InputMaybe<Scalars['Float']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
};

export type DateFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
  not?: InputMaybe<DateFilterInput>;
  eq?: InputMaybe<Scalars['Date']>;
  eqi?: InputMaybe<Scalars['Date']>;
  ne?: InputMaybe<Scalars['Date']>;
  nei?: InputMaybe<Scalars['Date']>;
  startsWith?: InputMaybe<Scalars['Date']>;
  endsWith?: InputMaybe<Scalars['Date']>;
  contains?: InputMaybe<Scalars['Date']>;
  notContains?: InputMaybe<Scalars['Date']>;
  containsi?: InputMaybe<Scalars['Date']>;
  notContainsi?: InputMaybe<Scalars['Date']>;
  gt?: InputMaybe<Scalars['Date']>;
  gte?: InputMaybe<Scalars['Date']>;
  lt?: InputMaybe<Scalars['Date']>;
  lte?: InputMaybe<Scalars['Date']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
};

export type DateTimeFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  not?: InputMaybe<DateTimeFilterInput>;
  eq?: InputMaybe<Scalars['DateTime']>;
  eqi?: InputMaybe<Scalars['DateTime']>;
  ne?: InputMaybe<Scalars['DateTime']>;
  nei?: InputMaybe<Scalars['DateTime']>;
  startsWith?: InputMaybe<Scalars['DateTime']>;
  endsWith?: InputMaybe<Scalars['DateTime']>;
  contains?: InputMaybe<Scalars['DateTime']>;
  notContains?: InputMaybe<Scalars['DateTime']>;
  containsi?: InputMaybe<Scalars['DateTime']>;
  notContainsi?: InputMaybe<Scalars['DateTime']>;
  gt?: InputMaybe<Scalars['DateTime']>;
  gte?: InputMaybe<Scalars['DateTime']>;
  lt?: InputMaybe<Scalars['DateTime']>;
  lte?: InputMaybe<Scalars['DateTime']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
};

export type JsonFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
  not?: InputMaybe<JsonFilterInput>;
  eq?: InputMaybe<Scalars['JSON']>;
  eqi?: InputMaybe<Scalars['JSON']>;
  ne?: InputMaybe<Scalars['JSON']>;
  nei?: InputMaybe<Scalars['JSON']>;
  startsWith?: InputMaybe<Scalars['JSON']>;
  endsWith?: InputMaybe<Scalars['JSON']>;
  contains?: InputMaybe<Scalars['JSON']>;
  notContains?: InputMaybe<Scalars['JSON']>;
  containsi?: InputMaybe<Scalars['JSON']>;
  notContainsi?: InputMaybe<Scalars['JSON']>;
  gt?: InputMaybe<Scalars['JSON']>;
  gte?: InputMaybe<Scalars['JSON']>;
  lt?: InputMaybe<Scalars['JSON']>;
  lte?: InputMaybe<Scalars['JSON']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
};

export type ComponentContentBlocksArticleList = {
  __typename?: 'ComponentContentBlocksArticleList';
  id: Scalars['ID'];
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
  categories?: Maybe<WebArticleCategoryRelationResponseCollection>;
};


export type ComponentContentBlocksArticleListCategoriesArgs = {
  filters?: InputMaybe<WebArticleCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ComponentContentBlocksBasicContentFiltersInput = {
  publishDate?: InputMaybe<DateTimeFilterInput>;
  unpublishDate?: InputMaybe<DateTimeFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentContentBlocksBasicContentFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentContentBlocksBasicContentFiltersInput>>>;
  not?: InputMaybe<ComponentContentBlocksBasicContentFiltersInput>;
};

export type ComponentContentBlocksBasicContent = {
  __typename?: 'ComponentContentBlocksBasicContent';
  id: Scalars['ID'];
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
};

export type ComponentContentBlocksContacts = {
  __typename?: 'ComponentContentBlocksContacts';
  id: Scalars['ID'];
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
};

export type ComponentContentBlocksHomeSplashScreen = {
  __typename?: 'ComponentContentBlocksHomeSplashScreen';
  id: Scalars['ID'];
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
  picture: UploadFileEntityResponse;
  content?: Maybe<Scalars['String']>;
};

export type ComponentContentBlocksPartners = {
  __typename?: 'ComponentContentBlocksPartners';
  id: Scalars['ID'];
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
};

export enum Enum_Componentcontentblocksregistrationform_Eventpick {
  None = 'None',
  SameRegion = 'SameRegion',
  SameTopRegion = 'SameTopRegion',
  Any = 'Any'
}

export type ComponentContentBlocksRegistrationForm = {
  __typename?: 'ComponentContentBlocksRegistrationForm';
  id: Scalars['ID'];
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
  registrationSuccessUrl?: Maybe<Scalars['String']>;
  schoolTypes?: Maybe<SchoolTypeRelationResponseCollection>;
  eventPick: Enum_Componentcontentblocksregistrationform_Eventpick;
  hasReferral: Scalars['Boolean'];
  hasTeacher: Scalars['Boolean'];
  hasCaptain: Scalars['Boolean'];
  minContestants: Scalars['Int'];
  maxContestants: Scalars['Int'];
  substituteContestants: Scalars['Int'];
  checkFields?: Maybe<Array<Maybe<ComponentMultimediaBlocksFormCheckField>>>;
  hasInvoice: Scalars['Boolean'];
};


export type ComponentContentBlocksRegistrationFormSchoolTypesArgs = {
  filters?: InputMaybe<SchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentContentBlocksRegistrationFormCheckFieldsArgs = {
  filters?: InputMaybe<ComponentMultimediaBlocksFormCheckFieldFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ComponentContentBlocksRegistrationSuccess = {
  __typename?: 'ComponentContentBlocksRegistrationSuccess';
  id: Scalars['ID'];
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
  refNextUrl?: Maybe<Scalars['String']>;
};

export type ComponentContentBlocksTimedContent = {
  __typename?: 'ComponentContentBlocksTimedContent';
  id: Scalars['ID'];
  content?: Maybe<Array<Maybe<ComponentContentBlocksBasicContent>>>;
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
};


export type ComponentContentBlocksTimedContentContentArgs = {
  filters?: InputMaybe<ComponentContentBlocksBasicContentFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ComponentDiscordDiscordIdListFiltersInput = {
  discordId?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentDiscordDiscordIdListFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentDiscordDiscordIdListFiltersInput>>>;
  not?: InputMaybe<ComponentDiscordDiscordIdListFiltersInput>;
};

export type ComponentDiscordDiscordIdListInput = {
  id?: InputMaybe<Scalars['ID']>;
  discordId?: InputMaybe<Scalars['String']>;
};

export type ComponentDiscordDiscordIdList = {
  __typename?: 'ComponentDiscordDiscordIdList';
  id: Scalars['ID'];
  discordId: Scalars['String'];
};

export type ComponentMultimediaBlocksCroppedImageFiltersInput = {
  x?: InputMaybe<IntFilterInput>;
  y?: InputMaybe<IntFilterInput>;
  width?: InputMaybe<IntFilterInput>;
  height?: InputMaybe<IntFilterInput>;
  projectType?: InputMaybe<ProjectTypeFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>>>;
  not?: InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>;
};

export type ComponentMultimediaBlocksCroppedImageInput = {
  id?: InputMaybe<Scalars['ID']>;
  image?: InputMaybe<Scalars['ID']>;
  x?: InputMaybe<Scalars['Int']>;
  y?: InputMaybe<Scalars['Int']>;
  width?: InputMaybe<Scalars['Int']>;
  height?: InputMaybe<Scalars['Int']>;
  projectType?: InputMaybe<Scalars['ID']>;
};

export type ComponentMultimediaBlocksCroppedImage = {
  __typename?: 'ComponentMultimediaBlocksCroppedImage';
  id: Scalars['ID'];
  image: UploadFileEntityResponse;
  x: Scalars['Int'];
  y: Scalars['Int'];
  width: Scalars['Int'];
  height: Scalars['Int'];
  projectType?: Maybe<ProjectTypeEntityResponse>;
};

export type ComponentMultimediaBlocksFormCheckFieldFiltersInput = {
  content?: InputMaybe<StringFilterInput>;
  isRequired?: InputMaybe<BooleanFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksFormCheckFieldFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksFormCheckFieldFiltersInput>>>;
  not?: InputMaybe<ComponentMultimediaBlocksFormCheckFieldFiltersInput>;
};

export type ComponentMultimediaBlocksFormCheckField = {
  __typename?: 'ComponentMultimediaBlocksFormCheckField';
  id: Scalars['ID'];
  content: Scalars['String'];
  isRequired: Scalars['Boolean'];
  name: Scalars['String'];
};

export type ComponentTdAFisherPhasesInfoPanelFiltersInput = {
  header?: InputMaybe<StringFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentTdAFisherPhasesInfoPanelFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentTdAFisherPhasesInfoPanelFiltersInput>>>;
  not?: InputMaybe<ComponentTdAFisherPhasesInfoPanelFiltersInput>;
};

export type ComponentTdAFisherPhasesInfoPanelInput = {
  id?: InputMaybe<Scalars['ID']>;
  header?: InputMaybe<Scalars['String']>;
  content?: InputMaybe<Scalars['String']>;
};

export type ComponentTdAFisherPhasesInfoPanel = {
  __typename?: 'ComponentTdAFisherPhasesInfoPanel';
  id: Scalars['ID'];
  header: Scalars['String'];
  content: Scalars['String'];
};

export type ComponentTdAFisherPhasesInfoFiltersInput = {
  links?: InputMaybe<ComponentTdAFisherPhasesLinkFiltersInput>;
  title?: InputMaybe<StringFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentTdAFisherPhasesInfoFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentTdAFisherPhasesInfoFiltersInput>>>;
  not?: InputMaybe<ComponentTdAFisherPhasesInfoFiltersInput>;
};

export type ComponentTdAFisherPhasesInfoInput = {
  id?: InputMaybe<Scalars['ID']>;
  links?: InputMaybe<Array<InputMaybe<ComponentTdAFisherPhasesLinkInput>>>;
  title?: InputMaybe<Scalars['String']>;
  content?: InputMaybe<Scalars['String']>;
};

export type ComponentTdAFisherPhasesInfo = {
  __typename?: 'ComponentTdAFisherPhasesInfo';
  id: Scalars['ID'];
  links?: Maybe<Array<Maybe<ComponentTdAFisherPhasesLink>>>;
  title?: Maybe<Scalars['String']>;
  content?: Maybe<Scalars['String']>;
};


export type ComponentTdAFisherPhasesInfoLinksArgs = {
  filters?: InputMaybe<ComponentTdAFisherPhasesLinkFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ComponentTdAFisherPhasesLinkFiltersInput = {
  name?: InputMaybe<StringFilterInput>;
  url?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentTdAFisherPhasesLinkFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentTdAFisherPhasesLinkFiltersInput>>>;
  not?: InputMaybe<ComponentTdAFisherPhasesLinkFiltersInput>;
};

export type ComponentTdAFisherPhasesLinkInput = {
  id?: InputMaybe<Scalars['ID']>;
  name?: InputMaybe<Scalars['String']>;
  url?: InputMaybe<Scalars['String']>;
};

export type ComponentTdAFisherPhasesLink = {
  __typename?: 'ComponentTdAFisherPhasesLink';
  id: Scalars['ID'];
  name: Scalars['String'];
  url: Scalars['String'];
};

export type ComponentTdaCtfComponentsEmail = {
  __typename?: 'ComponentTdaCtfComponentsEmail';
  id: Scalars['ID'];
  fromName?: Maybe<Scalars['String']>;
  fromEmail?: Maybe<Scalars['String']>;
  sentAt?: Maybe<Scalars['DateTime']>;
  subject?: Maybe<Scalars['String']>;
  content?: Maybe<Scalars['String']>;
};

export type ComponentTdaCtfComponentsGenericNote = {
  __typename?: 'ComponentTdaCtfComponentsGenericNote';
  id: Scalars['ID'];
  content?: Maybe<Scalars['String']>;
};

export type ComponentTdaCtfComponentsStickyNote = {
  __typename?: 'ComponentTdaCtfComponentsStickyNote';
  id: Scalars['ID'];
  header?: Maybe<Scalars['String']>;
  content?: Maybe<Scalars['String']>;
};

export type UploadFileFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  alternativeText?: InputMaybe<StringFilterInput>;
  caption?: InputMaybe<StringFilterInput>;
  width?: InputMaybe<IntFilterInput>;
  height?: InputMaybe<IntFilterInput>;
  formats?: InputMaybe<JsonFilterInput>;
  hash?: InputMaybe<StringFilterInput>;
  ext?: InputMaybe<StringFilterInput>;
  mime?: InputMaybe<StringFilterInput>;
  size?: InputMaybe<FloatFilterInput>;
  url?: InputMaybe<StringFilterInput>;
  previewUrl?: InputMaybe<StringFilterInput>;
  provider?: InputMaybe<StringFilterInput>;
  provider_metadata?: InputMaybe<JsonFilterInput>;
  folder?: InputMaybe<UploadFolderFiltersInput>;
  folderPath?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<UploadFileFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<UploadFileFiltersInput>>>;
  not?: InputMaybe<UploadFileFiltersInput>;
};

export type UploadFileInput = {
  name?: InputMaybe<Scalars['String']>;
  alternativeText?: InputMaybe<Scalars['String']>;
  caption?: InputMaybe<Scalars['String']>;
  width?: InputMaybe<Scalars['Int']>;
  height?: InputMaybe<Scalars['Int']>;
  formats?: InputMaybe<Scalars['JSON']>;
  hash?: InputMaybe<Scalars['String']>;
  ext?: InputMaybe<Scalars['String']>;
  mime?: InputMaybe<Scalars['String']>;
  size?: InputMaybe<Scalars['Float']>;
  url?: InputMaybe<Scalars['String']>;
  previewUrl?: InputMaybe<Scalars['String']>;
  provider?: InputMaybe<Scalars['String']>;
  provider_metadata?: InputMaybe<Scalars['JSON']>;
  folder?: InputMaybe<Scalars['ID']>;
  folderPath?: InputMaybe<Scalars['String']>;
};

export type UploadFile = {
  __typename?: 'UploadFile';
  name: Scalars['String'];
  alternativeText?: Maybe<Scalars['String']>;
  caption?: Maybe<Scalars['String']>;
  width?: Maybe<Scalars['Int']>;
  height?: Maybe<Scalars['Int']>;
  formats?: Maybe<Scalars['JSON']>;
  hash: Scalars['String'];
  ext?: Maybe<Scalars['String']>;
  mime: Scalars['String'];
  size: Scalars['Float'];
  url: Scalars['String'];
  previewUrl?: Maybe<Scalars['String']>;
  provider: Scalars['String'];
  provider_metadata?: Maybe<Scalars['JSON']>;
  related?: Maybe<Array<Maybe<GenericMorph>>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type UploadFileEntity = {
  __typename?: 'UploadFileEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<UploadFile>;
};

export type UploadFileEntityResponse = {
  __typename?: 'UploadFileEntityResponse';
  data?: Maybe<UploadFileEntity>;
};

export type UploadFileEntityResponseCollection = {
  __typename?: 'UploadFileEntityResponseCollection';
  data: Array<UploadFileEntity>;
  meta: ResponseCollectionMeta;
};

export type UploadFileRelationResponseCollection = {
  __typename?: 'UploadFileRelationResponseCollection';
  data: Array<UploadFileEntity>;
};

export type UploadFolderFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  pathId?: InputMaybe<IntFilterInput>;
  parent?: InputMaybe<UploadFolderFiltersInput>;
  children?: InputMaybe<UploadFolderFiltersInput>;
  files?: InputMaybe<UploadFileFiltersInput>;
  path?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<UploadFolderFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<UploadFolderFiltersInput>>>;
  not?: InputMaybe<UploadFolderFiltersInput>;
};

export type UploadFolderInput = {
  name?: InputMaybe<Scalars['String']>;
  pathId?: InputMaybe<Scalars['Int']>;
  parent?: InputMaybe<Scalars['ID']>;
  children?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  files?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  path?: InputMaybe<Scalars['String']>;
};

export type UploadFolder = {
  __typename?: 'UploadFolder';
  name: Scalars['String'];
  pathId: Scalars['Int'];
  parent?: Maybe<UploadFolderEntityResponse>;
  children?: Maybe<UploadFolderRelationResponseCollection>;
  files?: Maybe<UploadFileRelationResponseCollection>;
  path: Scalars['String'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type UploadFolderChildrenArgs = {
  filters?: InputMaybe<UploadFolderFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UploadFolderFilesArgs = {
  filters?: InputMaybe<UploadFileFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type UploadFolderEntity = {
  __typename?: 'UploadFolderEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<UploadFolder>;
};

export type UploadFolderEntityResponse = {
  __typename?: 'UploadFolderEntityResponse';
  data?: Maybe<UploadFolderEntity>;
};

export type UploadFolderEntityResponseCollection = {
  __typename?: 'UploadFolderEntityResponseCollection';
  data: Array<UploadFolderEntity>;
  meta: ResponseCollectionMeta;
};

export type UploadFolderRelationResponseCollection = {
  __typename?: 'UploadFolderRelationResponseCollection';
  data: Array<UploadFolderEntity>;
};

export type ContentReleasesReleaseFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  releasedAt?: InputMaybe<DateTimeFilterInput>;
  actions?: InputMaybe<ContentReleasesReleaseActionFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ContentReleasesReleaseFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ContentReleasesReleaseFiltersInput>>>;
  not?: InputMaybe<ContentReleasesReleaseFiltersInput>;
};

export type ContentReleasesReleaseInput = {
  name?: InputMaybe<Scalars['String']>;
  releasedAt?: InputMaybe<Scalars['DateTime']>;
  actions?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type ContentReleasesRelease = {
  __typename?: 'ContentReleasesRelease';
  name: Scalars['String'];
  releasedAt?: Maybe<Scalars['DateTime']>;
  actions?: Maybe<ContentReleasesReleaseActionRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type ContentReleasesReleaseActionsArgs = {
  filters?: InputMaybe<ContentReleasesReleaseActionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ContentReleasesReleaseEntity = {
  __typename?: 'ContentReleasesReleaseEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<ContentReleasesRelease>;
};

export type ContentReleasesReleaseEntityResponse = {
  __typename?: 'ContentReleasesReleaseEntityResponse';
  data?: Maybe<ContentReleasesReleaseEntity>;
};

export type ContentReleasesReleaseEntityResponseCollection = {
  __typename?: 'ContentReleasesReleaseEntityResponseCollection';
  data: Array<ContentReleasesReleaseEntity>;
  meta: ResponseCollectionMeta;
};

export enum Enum_Contentreleasesreleaseaction_Type {
  Publish = 'publish',
  Unpublish = 'unpublish'
}

export type ContentReleasesReleaseActionFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  type?: InputMaybe<StringFilterInput>;
  contentType?: InputMaybe<StringFilterInput>;
  release?: InputMaybe<ContentReleasesReleaseFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ContentReleasesReleaseActionFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ContentReleasesReleaseActionFiltersInput>>>;
  not?: InputMaybe<ContentReleasesReleaseActionFiltersInput>;
};

export type ContentReleasesReleaseActionInput = {
  type?: InputMaybe<Enum_Contentreleasesreleaseaction_Type>;
  contentType?: InputMaybe<Scalars['String']>;
  release?: InputMaybe<Scalars['ID']>;
};

export type ContentReleasesReleaseAction = {
  __typename?: 'ContentReleasesReleaseAction';
  type: Enum_Contentreleasesreleaseaction_Type;
  entry?: Maybe<GenericMorph>;
  contentType: Scalars['String'];
  release?: Maybe<ContentReleasesReleaseEntityResponse>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type ContentReleasesReleaseActionEntity = {
  __typename?: 'ContentReleasesReleaseActionEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<ContentReleasesReleaseAction>;
};

export type ContentReleasesReleaseActionEntityResponse = {
  __typename?: 'ContentReleasesReleaseActionEntityResponse';
  data?: Maybe<ContentReleasesReleaseActionEntity>;
};

export type ContentReleasesReleaseActionEntityResponseCollection = {
  __typename?: 'ContentReleasesReleaseActionEntityResponseCollection';
  data: Array<ContentReleasesReleaseActionEntity>;
  meta: ResponseCollectionMeta;
};

export type ContentReleasesReleaseActionRelationResponseCollection = {
  __typename?: 'ContentReleasesReleaseActionRelationResponseCollection';
  data: Array<ContentReleasesReleaseActionEntity>;
};

export type I18NLocaleFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  code?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<I18NLocaleFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<I18NLocaleFiltersInput>>>;
  not?: InputMaybe<I18NLocaleFiltersInput>;
};

export type I18NLocale = {
  __typename?: 'I18NLocale';
  name?: Maybe<Scalars['String']>;
  code?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type I18NLocaleEntity = {
  __typename?: 'I18NLocaleEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<I18NLocale>;
};

export type I18NLocaleEntityResponse = {
  __typename?: 'I18NLocaleEntityResponse';
  data?: Maybe<I18NLocaleEntity>;
};

export type I18NLocaleEntityResponseCollection = {
  __typename?: 'I18NLocaleEntityResponseCollection';
  data: Array<I18NLocaleEntity>;
  meta: ResponseCollectionMeta;
};

export type UsersPermissionsPermissionFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  action?: InputMaybe<StringFilterInput>;
  role?: InputMaybe<UsersPermissionsRoleFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<UsersPermissionsPermissionFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<UsersPermissionsPermissionFiltersInput>>>;
  not?: InputMaybe<UsersPermissionsPermissionFiltersInput>;
};

export type UsersPermissionsPermission = {
  __typename?: 'UsersPermissionsPermission';
  action: Scalars['String'];
  role?: Maybe<UsersPermissionsRoleEntityResponse>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type UsersPermissionsPermissionEntity = {
  __typename?: 'UsersPermissionsPermissionEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<UsersPermissionsPermission>;
};

export type UsersPermissionsPermissionRelationResponseCollection = {
  __typename?: 'UsersPermissionsPermissionRelationResponseCollection';
  data: Array<UsersPermissionsPermissionEntity>;
};

export type UsersPermissionsRoleFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  description?: InputMaybe<StringFilterInput>;
  type?: InputMaybe<StringFilterInput>;
  permissions?: InputMaybe<UsersPermissionsPermissionFiltersInput>;
  users?: InputMaybe<UsersPermissionsUserFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<UsersPermissionsRoleFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<UsersPermissionsRoleFiltersInput>>>;
  not?: InputMaybe<UsersPermissionsRoleFiltersInput>;
};

export type UsersPermissionsRoleInput = {
  name?: InputMaybe<Scalars['String']>;
  description?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Scalars['String']>;
  permissions?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  users?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type UsersPermissionsRole = {
  __typename?: 'UsersPermissionsRole';
  name: Scalars['String'];
  description?: Maybe<Scalars['String']>;
  type?: Maybe<Scalars['String']>;
  permissions?: Maybe<UsersPermissionsPermissionRelationResponseCollection>;
  users?: Maybe<UsersPermissionsUserRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type UsersPermissionsRolePermissionsArgs = {
  filters?: InputMaybe<UsersPermissionsPermissionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsRoleUsersArgs = {
  filters?: InputMaybe<UsersPermissionsUserFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type UsersPermissionsRoleEntity = {
  __typename?: 'UsersPermissionsRoleEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<UsersPermissionsRole>;
};

export type UsersPermissionsRoleEntityResponse = {
  __typename?: 'UsersPermissionsRoleEntityResponse';
  data?: Maybe<UsersPermissionsRoleEntity>;
};

export type UsersPermissionsRoleEntityResponseCollection = {
  __typename?: 'UsersPermissionsRoleEntityResponseCollection';
  data: Array<UsersPermissionsRoleEntity>;
  meta: ResponseCollectionMeta;
};

export enum Enum_Userspermissionsuser_State {
  Novacek = 'Novacek',
  Aktivni = 'Aktivni',
  Neaktivni = 'Neaktivni',
  Alumni = 'Alumni',
  Neznamo = 'Neznamo'
}

export type UsersPermissionsUserFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  username?: InputMaybe<StringFilterInput>;
  email?: InputMaybe<StringFilterInput>;
  provider?: InputMaybe<StringFilterInput>;
  password?: InputMaybe<StringFilterInput>;
  resetPasswordToken?: InputMaybe<StringFilterInput>;
  confirmationToken?: InputMaybe<StringFilterInput>;
  confirmed?: InputMaybe<BooleanFilterInput>;
  blocked?: InputMaybe<BooleanFilterInput>;
  role?: InputMaybe<UsersPermissionsRoleFiltersInput>;
  firstname?: InputMaybe<StringFilterInput>;
  lastname?: InputMaybe<StringFilterInput>;
  nickname?: InputMaybe<StringFilterInput>;
  phoneNumber?: InputMaybe<StringFilterInput>;
  bankAccountNumber?: InputMaybe<StringFilterInput>;
  birthday?: InputMaybe<DateFilterInput>;
  address?: InputMaybe<StringFilterInput>;
  isMember?: InputMaybe<BooleanFilterInput>;
  state?: InputMaybe<StringFilterInput>;
  profilePictures?: InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>;
  bio?: InputMaybe<StringFilterInput>;
  webArticles?: InputMaybe<WebArticleFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<UsersPermissionsUserFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<UsersPermissionsUserFiltersInput>>>;
  not?: InputMaybe<UsersPermissionsUserFiltersInput>;
};

export type UsersPermissionsUserInput = {
  username?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  provider?: InputMaybe<Scalars['String']>;
  password?: InputMaybe<Scalars['String']>;
  resetPasswordToken?: InputMaybe<Scalars['String']>;
  confirmationToken?: InputMaybe<Scalars['String']>;
  confirmed?: InputMaybe<Scalars['Boolean']>;
  blocked?: InputMaybe<Scalars['Boolean']>;
  role?: InputMaybe<Scalars['ID']>;
  firstname?: InputMaybe<Scalars['String']>;
  lastname?: InputMaybe<Scalars['String']>;
  nickname?: InputMaybe<Scalars['String']>;
  phoneNumber?: InputMaybe<Scalars['String']>;
  bankAccountNumber?: InputMaybe<Scalars['String']>;
  birthday?: InputMaybe<Scalars['Date']>;
  address?: InputMaybe<Scalars['String']>;
  isMember?: InputMaybe<Scalars['Boolean']>;
  state?: InputMaybe<Enum_Userspermissionsuser_State>;
  profilePictures?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksCroppedImageInput>>>;
  bio?: InputMaybe<Scalars['String']>;
  webArticles?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type UsersPermissionsUser = {
  __typename?: 'UsersPermissionsUser';
  username: Scalars['String'];
  email: Scalars['String'];
  provider?: Maybe<Scalars['String']>;
  confirmed?: Maybe<Scalars['Boolean']>;
  blocked?: Maybe<Scalars['Boolean']>;
  role?: Maybe<UsersPermissionsRoleEntityResponse>;
  firstname: Scalars['String'];
  lastname: Scalars['String'];
  nickname?: Maybe<Scalars['String']>;
  phoneNumber?: Maybe<Scalars['String']>;
  bankAccountNumber?: Maybe<Scalars['String']>;
  birthday?: Maybe<Scalars['Date']>;
  address?: Maybe<Scalars['String']>;
  isMember: Scalars['Boolean'];
  state: Enum_Userspermissionsuser_State;
  profilePictures?: Maybe<Array<Maybe<ComponentMultimediaBlocksCroppedImage>>>;
  bio?: Maybe<Scalars['String']>;
  webArticles?: Maybe<WebArticleRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type UsersPermissionsUserProfilePicturesArgs = {
  filters?: InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserWebArticlesArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  publicationState?: InputMaybe<PublicationState>;
};

export type UsersPermissionsUserEntity = {
  __typename?: 'UsersPermissionsUserEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<UsersPermissionsUser>;
};

export type UsersPermissionsUserEntityResponse = {
  __typename?: 'UsersPermissionsUserEntityResponse';
  data?: Maybe<UsersPermissionsUserEntity>;
};

export type UsersPermissionsUserEntityResponseCollection = {
  __typename?: 'UsersPermissionsUserEntityResponseCollection';
  data: Array<UsersPermissionsUserEntity>;
  meta: ResponseCollectionMeta;
};

export type UsersPermissionsUserRelationResponseCollection = {
  __typename?: 'UsersPermissionsUserRelationResponseCollection';
  data: Array<UsersPermissionsUserEntity>;
};

export enum Enum_Amosgamestatistic_Winner {
  X = 'x',
  O = 'o',
  Draw = 'draw'
}

export type AmosGameStatisticFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  language?: InputMaybe<StringFilterInput>;
  moves?: InputMaybe<JsonFilterInput>;
  movesCount?: InputMaybe<IntFilterInput>;
  board?: InputMaybe<JsonFilterInput>;
  winner?: InputMaybe<StringFilterInput>;
  duration?: InputMaybe<LongFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<AmosGameStatisticFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<AmosGameStatisticFiltersInput>>>;
  not?: InputMaybe<AmosGameStatisticFiltersInput>;
};

export type AmosGameStatisticInput = {
  language?: InputMaybe<Scalars['String']>;
  moves?: InputMaybe<Scalars['JSON']>;
  movesCount?: InputMaybe<Scalars['Int']>;
  board?: InputMaybe<Scalars['JSON']>;
  winner?: InputMaybe<Enum_Amosgamestatistic_Winner>;
  duration?: InputMaybe<Scalars['Long']>;
};

export type AmosGameStatistic = {
  __typename?: 'AmosGameStatistic';
  language: Scalars['String'];
  moves: Scalars['JSON'];
  movesCount: Scalars['Int'];
  board: Scalars['JSON'];
  winner: Enum_Amosgamestatistic_Winner;
  duration: Scalars['Long'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type AmosGameStatisticEntity = {
  __typename?: 'AmosGameStatisticEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<AmosGameStatistic>;
};

export type AmosGameStatisticEntityResponse = {
  __typename?: 'AmosGameStatisticEntityResponse';
  data?: Maybe<AmosGameStatisticEntity>;
};

export type AmosGameStatisticEntityResponseCollection = {
  __typename?: 'AmosGameStatisticEntityResponseCollection';
  data: Array<AmosGameStatisticEntity>;
  meta: ResponseCollectionMeta;
};

export enum Enum_Contestant_Role {
  TeamLeader = 'teamLeader',
  TeamMember = 'teamMember',
  TeamSubstitute = 'teamSubstitute'
}

export type ContestantFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  firstname?: InputMaybe<StringFilterInput>;
  lastname?: InputMaybe<StringFilterInput>;
  email?: InputMaybe<StringFilterInput>;
  phoneNumber?: InputMaybe<StringFilterInput>;
  school?: InputMaybe<SchoolFiltersInput>;
  role?: InputMaybe<StringFilterInput>;
  tdaTeam?: InputMaybe<TdaTeamFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ContestantFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ContestantFiltersInput>>>;
  not?: InputMaybe<ContestantFiltersInput>;
};

export type ContestantInput = {
  firstname?: InputMaybe<Scalars['String']>;
  lastname?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  phoneNumber?: InputMaybe<Scalars['String']>;
  school?: InputMaybe<Scalars['ID']>;
  role?: InputMaybe<Enum_Contestant_Role>;
  tdaTeam?: InputMaybe<Scalars['ID']>;
};

export type Contestant = {
  __typename?: 'Contestant';
  firstname: Scalars['String'];
  lastname: Scalars['String'];
  email: Scalars['String'];
  phoneNumber?: Maybe<Scalars['String']>;
  school?: Maybe<SchoolEntityResponse>;
  role: Enum_Contestant_Role;
  tdaTeam?: Maybe<TdaTeamEntityResponse>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type ContestantEntity = {
  __typename?: 'ContestantEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<Contestant>;
};

export type ContestantEntityResponse = {
  __typename?: 'ContestantEntityResponse';
  data?: Maybe<ContestantEntity>;
};

export type ContestantEntityResponseCollection = {
  __typename?: 'ContestantEntityResponseCollection';
  data: Array<ContestantEntity>;
  meta: ResponseCollectionMeta;
};

export type ContestantRelationResponseCollection = {
  __typename?: 'ContestantRelationResponseCollection';
  data: Array<ContestantEntity>;
};

export enum Enum_Event_State {
  Neotevrena = 'Neotevrena',
  Otevrena = 'Otevrena',
  Uzavrena = 'Uzavrena',
  Zrusena = 'Zrusena'
}

export type EventFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  eventGroup?: InputMaybe<EventGroupFiltersInput>;
  state?: InputMaybe<StringFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  school?: InputMaybe<SchoolFiltersInput>;
  place?: InputMaybe<StringFilterInput>;
  link?: InputMaybe<StringFilterInput>;
  startAt?: InputMaybe<DateTimeFilterInput>;
  capacity?: InputMaybe<IntFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<EventFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<EventFiltersInput>>>;
  not?: InputMaybe<EventFiltersInput>;
};

export type EventInput = {
  eventGroup?: InputMaybe<Scalars['ID']>;
  state?: InputMaybe<Enum_Event_State>;
  name?: InputMaybe<Scalars['String']>;
  school?: InputMaybe<Scalars['ID']>;
  place?: InputMaybe<Scalars['String']>;
  link?: InputMaybe<Scalars['String']>;
  startAt?: InputMaybe<Scalars['DateTime']>;
  capacity?: InputMaybe<Scalars['Int']>;
};

export type Event = {
  __typename?: 'Event';
  eventGroup?: Maybe<EventGroupEntityResponse>;
  state: Enum_Event_State;
  name?: Maybe<Scalars['String']>;
  school?: Maybe<SchoolEntityResponse>;
  place?: Maybe<Scalars['String']>;
  link?: Maybe<Scalars['String']>;
  startAt: Scalars['DateTime'];
  capacity?: Maybe<Scalars['Int']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type EventEntity = {
  __typename?: 'EventEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<Event>;
};

export type EventEntityResponse = {
  __typename?: 'EventEntityResponse';
  data?: Maybe<EventEntity>;
};

export type EventEntityResponseCollection = {
  __typename?: 'EventEntityResponseCollection';
  data: Array<EventEntity>;
  meta: ResponseCollectionMeta;
};

export type EventRelationResponseCollection = {
  __typename?: 'EventRelationResponseCollection';
  data: Array<EventEntity>;
};

export type EventGroupFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  project?: InputMaybe<ProjectFiltersInput>;
  name?: InputMaybe<StringFilterInput>;
  previousEventGroup?: InputMaybe<EventGroupFiltersInput>;
  events?: InputMaybe<EventFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<EventGroupFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<EventGroupFiltersInput>>>;
  not?: InputMaybe<EventGroupFiltersInput>;
};

export type EventGroupInput = {
  project?: InputMaybe<Scalars['ID']>;
  name?: InputMaybe<Scalars['String']>;
  previousEventGroup?: InputMaybe<Scalars['ID']>;
  events?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type EventGroup = {
  __typename?: 'EventGroup';
  project?: Maybe<ProjectEntityResponse>;
  name: Scalars['String'];
  previousEventGroup?: Maybe<EventGroupEntityResponse>;
  events?: Maybe<EventRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type EventGroupEventsArgs = {
  filters?: InputMaybe<EventFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type EventGroupEntity = {
  __typename?: 'EventGroupEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<EventGroup>;
};

export type EventGroupEntityResponse = {
  __typename?: 'EventGroupEntityResponse';
  data?: Maybe<EventGroupEntity>;
};

export type EventGroupEntityResponseCollection = {
  __typename?: 'EventGroupEntityResponseCollection';
  data: Array<EventGroupEntity>;
  meta: ResponseCollectionMeta;
};

export type EventGroupRelationResponseCollection = {
  __typename?: 'EventGroupRelationResponseCollection';
  data: Array<EventGroupEntity>;
};

export type ProjectFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  projectType?: InputMaybe<ProjectTypeFiltersInput>;
  year?: InputMaybe<IntFilterInput>;
  customName?: InputMaybe<StringFilterInput>;
  isRunning?: InputMaybe<BooleanFilterInput>;
  eventGroups?: InputMaybe<EventGroupFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ProjectFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ProjectFiltersInput>>>;
  not?: InputMaybe<ProjectFiltersInput>;
};

export type ProjectInput = {
  projectType?: InputMaybe<Scalars['ID']>;
  year?: InputMaybe<Scalars['Int']>;
  customName?: InputMaybe<Scalars['String']>;
  isRunning?: InputMaybe<Scalars['Boolean']>;
  eventGroups?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type Project = {
  __typename?: 'Project';
  projectType?: Maybe<ProjectTypeEntityResponse>;
  year: Scalars['Int'];
  customName?: Maybe<Scalars['String']>;
  isRunning: Scalars['Boolean'];
  eventGroups?: Maybe<EventGroupRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type ProjectEventGroupsArgs = {
  filters?: InputMaybe<EventGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ProjectEntity = {
  __typename?: 'ProjectEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<Project>;
};

export type ProjectEntityResponse = {
  __typename?: 'ProjectEntityResponse';
  data?: Maybe<ProjectEntity>;
};

export type ProjectEntityResponseCollection = {
  __typename?: 'ProjectEntityResponseCollection';
  data: Array<ProjectEntity>;
  meta: ResponseCollectionMeta;
};

export type ProjectRelationResponseCollection = {
  __typename?: 'ProjectRelationResponseCollection';
  data: Array<ProjectEntity>;
};

export type ProjectTypeFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  tagColor?: InputMaybe<StringFilterInput>;
  projects?: InputMaybe<ProjectFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ProjectTypeFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ProjectTypeFiltersInput>>>;
  not?: InputMaybe<ProjectTypeFiltersInput>;
};

export type ProjectTypeInput = {
  name?: InputMaybe<Scalars['String']>;
  tagColor?: InputMaybe<Scalars['String']>;
  projects?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type ProjectType = {
  __typename?: 'ProjectType';
  name: Scalars['String'];
  tagColor?: Maybe<Scalars['String']>;
  projects?: Maybe<ProjectRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type ProjectTypeProjectsArgs = {
  filters?: InputMaybe<ProjectFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ProjectTypeEntity = {
  __typename?: 'ProjectTypeEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<ProjectType>;
};

export type ProjectTypeEntityResponse = {
  __typename?: 'ProjectTypeEntityResponse';
  data?: Maybe<ProjectTypeEntity>;
};

export type ProjectTypeEntityResponseCollection = {
  __typename?: 'ProjectTypeEntityResponseCollection';
  data: Array<ProjectTypeEntity>;
  meta: ResponseCollectionMeta;
};

export type RegionFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  childRegions?: InputMaybe<RegionFiltersInput>;
  schools?: InputMaybe<SchoolFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<RegionFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<RegionFiltersInput>>>;
  not?: InputMaybe<RegionFiltersInput>;
};

export type RegionInput = {
  name?: InputMaybe<Scalars['String']>;
  childRegions?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  schools?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type Region = {
  __typename?: 'Region';
  name: Scalars['String'];
  childRegions?: Maybe<RegionRelationResponseCollection>;
  schools?: Maybe<SchoolRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type RegionChildRegionsArgs = {
  filters?: InputMaybe<RegionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type RegionSchoolsArgs = {
  filters?: InputMaybe<SchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  publicationState?: InputMaybe<PublicationState>;
};

export type RegionEntity = {
  __typename?: 'RegionEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<Region>;
};

export type RegionEntityResponse = {
  __typename?: 'RegionEntityResponse';
  data?: Maybe<RegionEntity>;
};

export type RegionEntityResponseCollection = {
  __typename?: 'RegionEntityResponseCollection';
  data: Array<RegionEntity>;
  meta: ResponseCollectionMeta;
};

export type RegionRelationResponseCollection = {
  __typename?: 'RegionRelationResponseCollection';
  data: Array<RegionEntity>;
};

export type SchoolFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  contactPhone?: InputMaybe<StringFilterInput>;
  street?: InputMaybe<StringFilterInput>;
  town?: InputMaybe<StringFilterInput>;
  postCode?: InputMaybe<IntFilterInput>;
  redizo?: InputMaybe<StringFilterInput>;
  web?: InputMaybe<StringFilterInput>;
  shortName?: InputMaybe<StringFilterInput>;
  schoolTypes?: InputMaybe<SchoolTypeFiltersInput>;
  latitude?: InputMaybe<FloatFilterInput>;
  longitude?: InputMaybe<FloatFilterInput>;
  region?: InputMaybe<RegionFiltersInput>;
  cin?: InputMaybe<StringFilterInput>;
  contactEmail?: InputMaybe<StringFilterInput>;
  ghostId?: InputMaybe<StringFilterInput>;
  schoolContacts?: InputMaybe<SchoolContactFiltersInput>;
  contestants?: InputMaybe<ContestantFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<SchoolFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<SchoolFiltersInput>>>;
  not?: InputMaybe<SchoolFiltersInput>;
};

export type SchoolInput = {
  name?: InputMaybe<Scalars['String']>;
  contactPhone?: InputMaybe<Scalars['String']>;
  street?: InputMaybe<Scalars['String']>;
  town?: InputMaybe<Scalars['String']>;
  postCode?: InputMaybe<Scalars['Int']>;
  redizo?: InputMaybe<Scalars['String']>;
  web?: InputMaybe<Scalars['String']>;
  shortName?: InputMaybe<Scalars['String']>;
  schoolTypes?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  latitude?: InputMaybe<Scalars['Float']>;
  longitude?: InputMaybe<Scalars['Float']>;
  region?: InputMaybe<Scalars['ID']>;
  cin?: InputMaybe<Scalars['String']>;
  contactEmail?: InputMaybe<Scalars['String']>;
  ghostId?: InputMaybe<Scalars['String']>;
  schoolContacts?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  contestants?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type School = {
  __typename?: 'School';
  name: Scalars['String'];
  contactPhone?: Maybe<Scalars['String']>;
  street?: Maybe<Scalars['String']>;
  town?: Maybe<Scalars['String']>;
  postCode?: Maybe<Scalars['Int']>;
  redizo?: Maybe<Scalars['String']>;
  web?: Maybe<Scalars['String']>;
  shortName?: Maybe<Scalars['String']>;
  schoolTypes?: Maybe<SchoolTypeRelationResponseCollection>;
  latitude?: Maybe<Scalars['Float']>;
  longitude?: Maybe<Scalars['Float']>;
  region?: Maybe<RegionEntityResponse>;
  cin?: Maybe<Scalars['String']>;
  contactEmail?: Maybe<Scalars['String']>;
  ghostId?: Maybe<Scalars['String']>;
  schoolContacts?: Maybe<SchoolContactRelationResponseCollection>;
  contestants?: Maybe<ContestantRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type SchoolSchoolTypesArgs = {
  filters?: InputMaybe<SchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type SchoolSchoolContactsArgs = {
  filters?: InputMaybe<SchoolContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type SchoolContestantsArgs = {
  filters?: InputMaybe<ContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type SchoolEntity = {
  __typename?: 'SchoolEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<School>;
};

export type SchoolEntityResponse = {
  __typename?: 'SchoolEntityResponse';
  data?: Maybe<SchoolEntity>;
};

export type SchoolEntityResponseCollection = {
  __typename?: 'SchoolEntityResponseCollection';
  data: Array<SchoolEntity>;
  meta: ResponseCollectionMeta;
};

export type SchoolRelationResponseCollection = {
  __typename?: 'SchoolRelationResponseCollection';
  data: Array<SchoolEntity>;
};

export type SchoolContactFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  firstname?: InputMaybe<StringFilterInput>;
  lastname?: InputMaybe<StringFilterInput>;
  email?: InputMaybe<StringFilterInput>;
  phoneNumber?: InputMaybe<StringFilterInput>;
  school?: InputMaybe<SchoolFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<SchoolContactFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<SchoolContactFiltersInput>>>;
  not?: InputMaybe<SchoolContactFiltersInput>;
};

export type SchoolContactInput = {
  firstname?: InputMaybe<Scalars['String']>;
  lastname?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  phoneNumber?: InputMaybe<Scalars['String']>;
  school?: InputMaybe<Scalars['ID']>;
};

export type SchoolContact = {
  __typename?: 'SchoolContact';
  firstname: Scalars['String'];
  lastname: Scalars['String'];
  email: Scalars['String'];
  phoneNumber?: Maybe<Scalars['String']>;
  school?: Maybe<SchoolEntityResponse>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type SchoolContactEntity = {
  __typename?: 'SchoolContactEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<SchoolContact>;
};

export type SchoolContactEntityResponse = {
  __typename?: 'SchoolContactEntityResponse';
  data?: Maybe<SchoolContactEntity>;
};

export type SchoolContactEntityResponseCollection = {
  __typename?: 'SchoolContactEntityResponseCollection';
  data: Array<SchoolContactEntity>;
  meta: ResponseCollectionMeta;
};

export type SchoolContactRelationResponseCollection = {
  __typename?: 'SchoolContactRelationResponseCollection';
  data: Array<SchoolContactEntity>;
};

export type SchoolTypeFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  schools?: InputMaybe<SchoolFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<SchoolTypeFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<SchoolTypeFiltersInput>>>;
  not?: InputMaybe<SchoolTypeFiltersInput>;
};

export type SchoolTypeInput = {
  name?: InputMaybe<Scalars['String']>;
  schools?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type SchoolType = {
  __typename?: 'SchoolType';
  name: Scalars['String'];
  schools?: Maybe<SchoolRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type SchoolTypeSchoolsArgs = {
  filters?: InputMaybe<SchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  publicationState?: InputMaybe<PublicationState>;
};

export type SchoolTypeEntity = {
  __typename?: 'SchoolTypeEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<SchoolType>;
};

export type SchoolTypeEntityResponse = {
  __typename?: 'SchoolTypeEntityResponse';
  data?: Maybe<SchoolTypeEntity>;
};

export type SchoolTypeEntityResponseCollection = {
  __typename?: 'SchoolTypeEntityResponseCollection';
  data: Array<SchoolTypeEntity>;
  meta: ResponseCollectionMeta;
};

export type SchoolTypeRelationResponseCollection = {
  __typename?: 'SchoolTypeRelationResponseCollection';
  data: Array<SchoolTypeEntity>;
};

export type TdaBotInput = {
  adminRoleId?: InputMaybe<Scalars['String']>;
  modRoleId?: InputMaybe<Scalars['String']>;
  modLogChannelId?: InputMaybe<Scalars['String']>;
  guildId?: InputMaybe<Scalars['String']>;
  tagBannedUsers?: InputMaybe<Array<InputMaybe<ComponentDiscordDiscordIdListInput>>>;
  statusText?: InputMaybe<Scalars['String']>;
  statusType?: InputMaybe<Scalars['Int']>;
  orgRoleId?: InputMaybe<Scalars['String']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaBot = {
  __typename?: 'TdaBot';
  adminRoleId?: Maybe<Scalars['String']>;
  modRoleId?: Maybe<Scalars['String']>;
  modLogChannelId?: Maybe<Scalars['String']>;
  guildId: Scalars['String'];
  tagBannedUsers?: Maybe<Array<Maybe<ComponentDiscordDiscordIdList>>>;
  statusText?: Maybe<Scalars['String']>;
  statusType: Scalars['Int'];
  orgRoleId: Scalars['String'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaBotTagBannedUsersArgs = {
  filters?: InputMaybe<ComponentDiscordDiscordIdListFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaBotEntity = {
  __typename?: 'TdaBotEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaBot>;
};

export type TdaBotEntityResponse = {
  __typename?: 'TdaBotEntityResponse';
  data?: Maybe<TdaBotEntity>;
};

export type TdaBotContestantFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  userId?: InputMaybe<StringFilterInput>;
  team?: InputMaybe<TdaTeamFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaBotContestantFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaBotContestantFiltersInput>>>;
  not?: InputMaybe<TdaBotContestantFiltersInput>;
};

export type TdaBotContestantInput = {
  userId?: InputMaybe<Scalars['String']>;
  team?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaBotContestant = {
  __typename?: 'TdaBotContestant';
  userId: Scalars['String'];
  team?: Maybe<TdaTeamEntityResponse>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaBotContestantEntity = {
  __typename?: 'TdaBotContestantEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaBotContestant>;
};

export type TdaBotContestantEntityResponse = {
  __typename?: 'TdaBotContestantEntityResponse';
  data?: Maybe<TdaBotContestantEntity>;
};

export type TdaBotContestantEntityResponseCollection = {
  __typename?: 'TdaBotContestantEntityResponseCollection';
  data: Array<TdaBotContestantEntity>;
  meta: ResponseCollectionMeta;
};

export type TdaBotTagFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  modOnly?: InputMaybe<BooleanFilterInput>;
  keyphrase?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaBotTagFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaBotTagFiltersInput>>>;
  not?: InputMaybe<TdaBotTagFiltersInput>;
};

export type TdaBotTagInput = {
  name?: InputMaybe<Scalars['String']>;
  content?: InputMaybe<Scalars['String']>;
  modOnly?: InputMaybe<Scalars['Boolean']>;
  keyphrase?: InputMaybe<Scalars['String']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaBotTag = {
  __typename?: 'TdaBotTag';
  name?: Maybe<Scalars['String']>;
  content?: Maybe<Scalars['String']>;
  modOnly: Scalars['Boolean'];
  keyphrase?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaBotTagEntity = {
  __typename?: 'TdaBotTagEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaBotTag>;
};

export type TdaBotTagEntityResponse = {
  __typename?: 'TdaBotTagEntityResponse';
  data?: Maybe<TdaBotTagEntity>;
};

export type TdaBotTagEntityResponseCollection = {
  __typename?: 'TdaBotTagEntityResponseCollection';
  data: Array<TdaBotTagEntity>;
  meta: ResponseCollectionMeta;
};

export enum Enum_Tdactffile_Type {
  Txt = 'txt',
  Bash = 'bash',
  Ctf = 'ctf',
  Folder = 'folder',
  FlagLeaderboard = 'flagLeaderboard'
}

export type TdaCtfFileFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  type?: InputMaybe<StringFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  owningUser?: InputMaybe<StringFilterInput>;
  owningGroup?: InputMaybe<StringFilterInput>;
  dateOfLastEdit?: InputMaybe<DateTimeFilterInput>;
  permission?: InputMaybe<StringFilterInput>;
  tda_ctf_folders?: InputMaybe<TdaCtfFolderFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaCtfFileFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaCtfFileFiltersInput>>>;
  not?: InputMaybe<TdaCtfFileFiltersInput>;
};

export type TdaCtfFileInput = {
  name?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Enum_Tdactffile_Type>;
  content?: InputMaybe<Scalars['String']>;
  owningUser?: InputMaybe<Scalars['String']>;
  owningGroup?: InputMaybe<Scalars['String']>;
  dateOfLastEdit?: InputMaybe<Scalars['DateTime']>;
  permission?: InputMaybe<Scalars['String']>;
  tda_ctf_folders?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type TdaCtfFile = {
  __typename?: 'TdaCtfFile';
  name: Scalars['String'];
  type: Enum_Tdactffile_Type;
  content?: Maybe<Scalars['String']>;
  owningUser: Scalars['String'];
  owningGroup: Scalars['String'];
  dateOfLastEdit: Scalars['DateTime'];
  permission: Scalars['String'];
  tda_ctf_folders?: Maybe<TdaCtfFolderRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaCtfFileTda_Ctf_FoldersArgs = {
  filters?: InputMaybe<TdaCtfFolderFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaCtfFileEntity = {
  __typename?: 'TdaCtfFileEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaCtfFile>;
};

export type TdaCtfFileEntityResponse = {
  __typename?: 'TdaCtfFileEntityResponse';
  data?: Maybe<TdaCtfFileEntity>;
};

export type TdaCtfFileEntityResponseCollection = {
  __typename?: 'TdaCtfFileEntityResponseCollection';
  data: Array<TdaCtfFileEntity>;
  meta: ResponseCollectionMeta;
};

export type TdaCtfFolderFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  owningUser?: InputMaybe<StringFilterInput>;
  owningGroup?: InputMaybe<StringFilterInput>;
  dateOfLastEdit?: InputMaybe<DateTimeFilterInput>;
  permission?: InputMaybe<StringFilterInput>;
  tda_ctf_child_folders?: InputMaybe<TdaCtfFolderFiltersInput>;
  tda_ctf_file?: InputMaybe<TdaCtfFileFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaCtfFolderFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaCtfFolderFiltersInput>>>;
  not?: InputMaybe<TdaCtfFolderFiltersInput>;
};

export type TdaCtfFolderInput = {
  name?: InputMaybe<Scalars['String']>;
  owningUser?: InputMaybe<Scalars['String']>;
  owningGroup?: InputMaybe<Scalars['String']>;
  dateOfLastEdit?: InputMaybe<Scalars['DateTime']>;
  permission?: InputMaybe<Scalars['String']>;
  tda_ctf_child_folders?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  tda_ctf_file?: InputMaybe<Scalars['ID']>;
};

export type TdaCtfFolder = {
  __typename?: 'TdaCtfFolder';
  name?: Maybe<Scalars['String']>;
  owningUser: Scalars['String'];
  owningGroup: Scalars['String'];
  dateOfLastEdit: Scalars['DateTime'];
  permission?: Maybe<Scalars['String']>;
  tda_ctf_child_folders?: Maybe<TdaCtfFolderRelationResponseCollection>;
  tda_ctf_file?: Maybe<TdaCtfFileEntityResponse>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaCtfFolderTda_Ctf_Child_FoldersArgs = {
  filters?: InputMaybe<TdaCtfFolderFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaCtfFolderEntity = {
  __typename?: 'TdaCtfFolderEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaCtfFolder>;
};

export type TdaCtfFolderEntityResponse = {
  __typename?: 'TdaCtfFolderEntityResponse';
  data?: Maybe<TdaCtfFolderEntity>;
};

export type TdaCtfFolderEntityResponseCollection = {
  __typename?: 'TdaCtfFolderEntityResponseCollection';
  data: Array<TdaCtfFolderEntity>;
  meta: ResponseCollectionMeta;
};

export type TdaCtfFolderRelationResponseCollection = {
  __typename?: 'TdaCtfFolderRelationResponseCollection';
  data: Array<TdaCtfFolderEntity>;
};

export type TdaFisherInfoPanelInput = {
  header?: InputMaybe<Scalars['String']>;
  description?: InputMaybe<Scalars['String']>;
  content?: InputMaybe<Array<InputMaybe<ComponentTdAFisherPhasesInfoPanelInput>>>;
};

export type TdaFisherInfoPanel = {
  __typename?: 'TdaFisherInfoPanel';
  header: Scalars['String'];
  description: Scalars['String'];
  content?: Maybe<Array<Maybe<ComponentTdAFisherPhasesInfoPanel>>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaFisherInfoPanelContentArgs = {
  filters?: InputMaybe<ComponentTdAFisherPhasesInfoPanelFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaFisherInfoPanelEntity = {
  __typename?: 'TdaFisherInfoPanelEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaFisherInfoPanel>;
};

export type TdaFisherInfoPanelEntityResponse = {
  __typename?: 'TdaFisherInfoPanelEntityResponse';
  data?: Maybe<TdaFisherInfoPanelEntity>;
};

export enum Enum_Tdaflag_Stage {
  Nk = 'NK',
  Sk = 'SK',
  Grf = 'GRF'
}

export enum Enum_Tdaflag_Typeofnote {
  Generic = 'generic',
  Email = 'email',
  StickyNote = 'stickyNote',
  Notepad = 'notepad'
}

export type TdaFlagNoteDynamicZone = ComponentTdaCtfComponentsEmail | ComponentTdaCtfComponentsGenericNote | ComponentTdaCtfComponentsStickyNote | Error;

export type TdaFlagFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  flag?: InputMaybe<StringFilterInput>;
  stage?: InputMaybe<StringFilterInput>;
  basePoints?: InputMaybe<IntFilterInput>;
  typeOfNote?: InputMaybe<StringFilterInput>;
  help?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaFlagFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaFlagFiltersInput>>>;
  not?: InputMaybe<TdaFlagFiltersInput>;
};

export type TdaFlagInput = {
  name?: InputMaybe<Scalars['String']>;
  flag?: InputMaybe<Scalars['String']>;
  stage?: InputMaybe<Enum_Tdaflag_Stage>;
  basePoints?: InputMaybe<Scalars['Int']>;
  typeOfNote?: InputMaybe<Enum_Tdaflag_Typeofnote>;
  help?: InputMaybe<Scalars['String']>;
  note?: InputMaybe<Array<Scalars['TdaFlagNoteDynamicZoneInput']>>;
};

export type TdaFlag = {
  __typename?: 'TdaFlag';
  name: Scalars['String'];
  flag: Scalars['String'];
  stage: Enum_Tdaflag_Stage;
  basePoints: Scalars['Int'];
  typeOfNote?: Maybe<Enum_Tdaflag_Typeofnote>;
  help?: Maybe<Scalars['String']>;
  note?: Maybe<Array<Maybe<TdaFlagNoteDynamicZone>>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaFlagEntity = {
  __typename?: 'TdaFlagEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaFlag>;
};

export type TdaFlagEntityResponse = {
  __typename?: 'TdaFlagEntityResponse';
  data?: Maybe<TdaFlagEntity>;
};

export type TdaFlagEntityResponseCollection = {
  __typename?: 'TdaFlagEntityResponseCollection';
  data: Array<TdaFlagEntity>;
  meta: ResponseCollectionMeta;
};

export enum Enum_Tdaphase_State {
  Undefined = 'undefined',
  PreNk = 'PRE_NK',
  Nk = 'NK',
  PostNk = 'POST_NK',
  PreSk = 'PRE_SK',
  Sk = 'SK',
  PostSk = 'POST_SK',
  PreGrf = 'PRE_GRF',
  Grf = 'GRF',
  PostGrf = 'POST_GRF'
}

export type TdaPhaseFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  startsAt?: InputMaybe<DateTimeFilterInput>;
  endsAt?: InputMaybe<DateTimeFilterInput>;
  state?: InputMaybe<StringFilterInput>;
  infoPanels?: InputMaybe<ComponentTdAFisherPhasesInfoFiltersInput>;
  tda_tests?: InputMaybe<TdaTestFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaPhaseFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaPhaseFiltersInput>>>;
  not?: InputMaybe<TdaPhaseFiltersInput>;
};

export type TdaPhaseInput = {
  startsAt?: InputMaybe<Scalars['DateTime']>;
  endsAt?: InputMaybe<Scalars['DateTime']>;
  state?: InputMaybe<Enum_Tdaphase_State>;
  infoPanels?: InputMaybe<Array<InputMaybe<ComponentTdAFisherPhasesInfoInput>>>;
  tda_tests?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type TdaPhase = {
  __typename?: 'TdaPhase';
  startsAt: Scalars['DateTime'];
  endsAt: Scalars['DateTime'];
  state: Enum_Tdaphase_State;
  infoPanels?: Maybe<Array<Maybe<ComponentTdAFisherPhasesInfo>>>;
  tda_tests?: Maybe<TdaTestRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaPhaseInfoPanelsArgs = {
  filters?: InputMaybe<ComponentTdAFisherPhasesInfoFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaPhaseTda_TestsArgs = {
  filters?: InputMaybe<TdaTestFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaPhaseEntity = {
  __typename?: 'TdaPhaseEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaPhase>;
};

export type TdaPhaseEntityResponse = {
  __typename?: 'TdaPhaseEntityResponse';
  data?: Maybe<TdaPhaseEntity>;
};

export type TdaPhaseEntityResponseCollection = {
  __typename?: 'TdaPhaseEntityResponseCollection';
  data: Array<TdaPhaseEntity>;
  meta: ResponseCollectionMeta;
};

export type TdaSessionFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  sessionId?: InputMaybe<StringFilterInput>;
  team?: InputMaybe<TdaTeamFiltersInput>;
  expiration?: InputMaybe<DateTimeFilterInput>;
  userId?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaSessionFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaSessionFiltersInput>>>;
  not?: InputMaybe<TdaSessionFiltersInput>;
};

export type TdaSessionInput = {
  sessionId?: InputMaybe<Scalars['String']>;
  team?: InputMaybe<Scalars['ID']>;
  expiration?: InputMaybe<Scalars['DateTime']>;
  userId?: InputMaybe<Scalars['String']>;
};

export type TdaSession = {
  __typename?: 'TdaSession';
  sessionId: Scalars['String'];
  team?: Maybe<TdaTeamEntityResponse>;
  expiration: Scalars['DateTime'];
  userId?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaSessionEntity = {
  __typename?: 'TdaSessionEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaSession>;
};

export type TdaSessionEntityResponse = {
  __typename?: 'TdaSessionEntityResponse';
  data?: Maybe<TdaSessionEntity>;
};

export type TdaSessionEntityResponseCollection = {
  __typename?: 'TdaSessionEntityResponseCollection';
  data: Array<TdaSessionEntity>;
  meta: ResponseCollectionMeta;
};

export type TdaTeamFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  url?: InputMaybe<StringFilterInput>;
  access?: InputMaybe<BooleanFilterInput>;
  uuid?: InputMaybe<StringFilterInput>;
  ghostID?: InputMaybe<IntFilterInput>;
  subdomain?: InputMaybe<StringFilterInput>;
  ownReferral?: InputMaybe<StringFilterInput>;
  usedReferral?: InputMaybe<StringFilterInput>;
  schoolContact?: InputMaybe<SchoolContactFiltersInput>;
  contestants?: InputMaybe<ContestantFiltersInput>;
  allowedPartnerDataAccess?: InputMaybe<BooleanFilterInput>;
  discordCode?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaTeamFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaTeamFiltersInput>>>;
  not?: InputMaybe<TdaTeamFiltersInput>;
};

export type TdaTeamInput = {
  name?: InputMaybe<Scalars['String']>;
  url?: InputMaybe<Scalars['String']>;
  access?: InputMaybe<Scalars['Boolean']>;
  uuid?: InputMaybe<Scalars['String']>;
  ghostID?: InputMaybe<Scalars['Int']>;
  subdomain?: InputMaybe<Scalars['String']>;
  ownReferral?: InputMaybe<Scalars['String']>;
  usedReferral?: InputMaybe<Scalars['String']>;
  schoolContact?: InputMaybe<Scalars['ID']>;
  contestants?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  allowedPartnerDataAccess?: InputMaybe<Scalars['Boolean']>;
  discordCode?: InputMaybe<Scalars['String']>;
};

export type TdaTeam = {
  __typename?: 'TdaTeam';
  name: Scalars['String'];
  url?: Maybe<Scalars['String']>;
  access: Scalars['Boolean'];
  uuid: Scalars['String'];
  ghostID?: Maybe<Scalars['Int']>;
  subdomain?: Maybe<Scalars['String']>;
  ownReferral?: Maybe<Scalars['String']>;
  usedReferral?: Maybe<Scalars['String']>;
  schoolContact?: Maybe<SchoolContactEntityResponse>;
  contestants?: Maybe<ContestantRelationResponseCollection>;
  allowedPartnerDataAccess: Scalars['Boolean'];
  discordCode: Scalars['String'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaTeamContestantsArgs = {
  filters?: InputMaybe<ContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaTeamEntity = {
  __typename?: 'TdaTeamEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaTeam>;
};

export type TdaTeamEntityResponse = {
  __typename?: 'TdaTeamEntityResponse';
  data?: Maybe<TdaTeamEntity>;
};

export type TdaTeamEntityResponseCollection = {
  __typename?: 'TdaTeamEntityResponseCollection';
  data: Array<TdaTeamEntity>;
  meta: ResponseCollectionMeta;
};

export type TdaTeamFlagFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  team?: InputMaybe<TdaTeamFiltersInput>;
  flag?: InputMaybe<TdaFlagFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaTeamFlagFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaTeamFlagFiltersInput>>>;
  not?: InputMaybe<TdaTeamFlagFiltersInput>;
};

export type TdaTeamFlagInput = {
  team?: InputMaybe<Scalars['ID']>;
  flag?: InputMaybe<Scalars['ID']>;
};

export type TdaTeamFlag = {
  __typename?: 'TdaTeamFlag';
  team?: Maybe<TdaTeamEntityResponse>;
  flag?: Maybe<TdaFlagEntityResponse>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaTeamFlagEntity = {
  __typename?: 'TdaTeamFlagEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaTeamFlag>;
};

export type TdaTeamFlagEntityResponse = {
  __typename?: 'TdaTeamFlagEntityResponse';
  data?: Maybe<TdaTeamFlagEntity>;
};

export type TdaTeamFlagEntityResponseCollection = {
  __typename?: 'TdaTeamFlagEntityResponseCollection';
  data: Array<TdaTeamFlagEntity>;
  meta: ResponseCollectionMeta;
};

export type TdaTeamTestFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  team?: InputMaybe<TdaTeamFiltersInput>;
  test?: InputMaybe<TdaTestFiltersInput>;
  result?: InputMaybe<FloatFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaTeamTestFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaTeamTestFiltersInput>>>;
  not?: InputMaybe<TdaTeamTestFiltersInput>;
};

export type TdaTeamTestInput = {
  team?: InputMaybe<Scalars['ID']>;
  test?: InputMaybe<Scalars['ID']>;
  result?: InputMaybe<Scalars['Float']>;
};

export type TdaTeamTest = {
  __typename?: 'TdaTeamTest';
  team?: Maybe<TdaTeamEntityResponse>;
  test?: Maybe<TdaTestEntityResponse>;
  result: Scalars['Float'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaTeamTestEntity = {
  __typename?: 'TdaTeamTestEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaTeamTest>;
};

export type TdaTeamTestEntityResponse = {
  __typename?: 'TdaTeamTestEntityResponse';
  data?: Maybe<TdaTeamTestEntity>;
};

export type TdaTeamTestEntityResponseCollection = {
  __typename?: 'TdaTeamTestEntityResponseCollection';
  data: Array<TdaTeamTestEntity>;
  meta: ResponseCollectionMeta;
};

export enum Enum_Tdatest_Type {
  BunTest = 'bunTest',
  PyTest = 'pyTest'
}

export type TdaTestFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  description?: InputMaybe<StringFilterInput>;
  startsAt?: InputMaybe<DateTimeFilterInput>;
  endsAt?: InputMaybe<DateTimeFilterInput>;
  filename?: InputMaybe<StringFilterInput>;
  type?: InputMaybe<StringFilterInput>;
  maxPoints?: InputMaybe<IntFilterInput>;
  shortDescription?: InputMaybe<StringFilterInput>;
  tda_phase?: InputMaybe<TdaPhaseFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaTestFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaTestFiltersInput>>>;
  not?: InputMaybe<TdaTestFiltersInput>;
};

export type TdaTestInput = {
  name?: InputMaybe<Scalars['String']>;
  description?: InputMaybe<Scalars['String']>;
  startsAt?: InputMaybe<Scalars['DateTime']>;
  endsAt?: InputMaybe<Scalars['DateTime']>;
  filename?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Enum_Tdatest_Type>;
  maxPoints?: InputMaybe<Scalars['Int']>;
  shortDescription?: InputMaybe<Scalars['String']>;
  tda_phase?: InputMaybe<Scalars['ID']>;
};

export type TdaTest = {
  __typename?: 'TdaTest';
  name: Scalars['String'];
  description?: Maybe<Scalars['String']>;
  startsAt: Scalars['DateTime'];
  endsAt: Scalars['DateTime'];
  filename: Scalars['String'];
  type: Enum_Tdatest_Type;
  maxPoints?: Maybe<Scalars['Int']>;
  shortDescription?: Maybe<Scalars['String']>;
  tda_phase?: Maybe<TdaPhaseEntityResponse>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaTestEntity = {
  __typename?: 'TdaTestEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<TdaTest>;
};

export type TdaTestEntityResponse = {
  __typename?: 'TdaTestEntityResponse';
  data?: Maybe<TdaTestEntity>;
};

export type TdaTestEntityResponseCollection = {
  __typename?: 'TdaTestEntityResponseCollection';
  data: Array<TdaTestEntity>;
  meta: ResponseCollectionMeta;
};

export type TdaTestRelationResponseCollection = {
  __typename?: 'TdaTestRelationResponseCollection';
  data: Array<TdaTestEntity>;
};

export type WebArticleFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  publishDate?: InputMaybe<DateTimeFilterInput>;
  unpublishDate?: InputMaybe<DateTimeFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  hasFullHtmlControl?: InputMaybe<BooleanFilterInput>;
  showAuthor?: InputMaybe<BooleanFilterInput>;
  author?: InputMaybe<UsersPermissionsUserFiltersInput>;
  title?: InputMaybe<StringFilterInput>;
  anotation?: InputMaybe<StringFilterInput>;
  slug?: InputMaybe<StringFilterInput>;
  articleCategory?: InputMaybe<WebArticleCategoryFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  localizations?: InputMaybe<WebArticleFiltersInput>;
  locale?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<WebArticleFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebArticleFiltersInput>>>;
  not?: InputMaybe<WebArticleFiltersInput>;
};

export type WebArticleInput = {
  publishDate?: InputMaybe<Scalars['DateTime']>;
  unpublishDate?: InputMaybe<Scalars['DateTime']>;
  content?: InputMaybe<Scalars['String']>;
  hasFullHtmlControl?: InputMaybe<Scalars['Boolean']>;
  showAuthor?: InputMaybe<Scalars['Boolean']>;
  author?: InputMaybe<Scalars['ID']>;
  coverImage?: InputMaybe<Scalars['ID']>;
  title?: InputMaybe<Scalars['String']>;
  anotation?: InputMaybe<Scalars['String']>;
  slug?: InputMaybe<Scalars['String']>;
  articleCategory?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type WebArticle = {
  __typename?: 'WebArticle';
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
  hasFullHtmlControl: Scalars['Boolean'];
  showAuthor: Scalars['Boolean'];
  author?: Maybe<UsersPermissionsUserEntityResponse>;
  coverImage?: Maybe<UploadFileEntityResponse>;
  title: Scalars['String'];
  anotation?: Maybe<Scalars['String']>;
  slug: Scalars['String'];
  articleCategory?: Maybe<WebArticleCategoryEntityResponse>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  localizations?: Maybe<WebArticleRelationResponseCollection>;
  locale?: Maybe<Scalars['String']>;
};


export type WebArticleLocalizationsArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  publicationState?: InputMaybe<PublicationState>;
};

export type WebArticleEntity = {
  __typename?: 'WebArticleEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<WebArticle>;
};

export type WebArticleEntityResponse = {
  __typename?: 'WebArticleEntityResponse';
  data?: Maybe<WebArticleEntity>;
};

export type WebArticleEntityResponseCollection = {
  __typename?: 'WebArticleEntityResponseCollection';
  data: Array<WebArticleEntity>;
  meta: ResponseCollectionMeta;
};

export type WebArticleRelationResponseCollection = {
  __typename?: 'WebArticleRelationResponseCollection';
  data: Array<WebArticleEntity>;
};

export type WebArticleCategoryFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  articles?: InputMaybe<WebArticleFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  localizations?: InputMaybe<WebArticleCategoryFiltersInput>;
  locale?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<WebArticleCategoryFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebArticleCategoryFiltersInput>>>;
  not?: InputMaybe<WebArticleCategoryFiltersInput>;
};

export type WebArticleCategoryInput = {
  name?: InputMaybe<Scalars['String']>;
  defaultCoverImage?: InputMaybe<Scalars['ID']>;
  articles?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type WebArticleCategory = {
  __typename?: 'WebArticleCategory';
  name: Scalars['String'];
  defaultCoverImage: UploadFileEntityResponse;
  articles?: Maybe<WebArticleRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  localizations?: Maybe<WebArticleCategoryRelationResponseCollection>;
  locale?: Maybe<Scalars['String']>;
};


export type WebArticleCategoryArticlesArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  publicationState?: InputMaybe<PublicationState>;
};


export type WebArticleCategoryLocalizationsArgs = {
  filters?: InputMaybe<WebArticleCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebArticleCategoryEntity = {
  __typename?: 'WebArticleCategoryEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<WebArticleCategory>;
};

export type WebArticleCategoryEntityResponse = {
  __typename?: 'WebArticleCategoryEntityResponse';
  data?: Maybe<WebArticleCategoryEntity>;
};

export type WebArticleCategoryEntityResponseCollection = {
  __typename?: 'WebArticleCategoryEntityResponseCollection';
  data: Array<WebArticleCategoryEntity>;
  meta: ResponseCollectionMeta;
};

export type WebArticleCategoryRelationResponseCollection = {
  __typename?: 'WebArticleCategoryRelationResponseCollection';
  data: Array<WebArticleCategoryEntity>;
};

export type WebContactFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  webContactGroup?: InputMaybe<WebContactGroupFiltersInput>;
  roleName?: InputMaybe<StringFilterInput>;
  user?: InputMaybe<UsersPermissionsUserFiltersInput>;
  weight?: InputMaybe<IntFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  localizations?: InputMaybe<WebContactFiltersInput>;
  locale?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<WebContactFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebContactFiltersInput>>>;
  not?: InputMaybe<WebContactFiltersInput>;
};

export type WebContactInput = {
  webContactGroup?: InputMaybe<Scalars['ID']>;
  roleName?: InputMaybe<Scalars['String']>;
  user?: InputMaybe<Scalars['ID']>;
  weight?: InputMaybe<Scalars['Int']>;
};

export type WebContact = {
  __typename?: 'WebContact';
  webContactGroup?: Maybe<WebContactGroupEntityResponse>;
  roleName?: Maybe<Scalars['String']>;
  user?: Maybe<UsersPermissionsUserEntityResponse>;
  weight: Scalars['Int'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  localizations?: Maybe<WebContactRelationResponseCollection>;
  locale?: Maybe<Scalars['String']>;
};


export type WebContactLocalizationsArgs = {
  filters?: InputMaybe<WebContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebContactEntity = {
  __typename?: 'WebContactEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<WebContact>;
};

export type WebContactEntityResponse = {
  __typename?: 'WebContactEntityResponse';
  data?: Maybe<WebContactEntity>;
};

export type WebContactEntityResponseCollection = {
  __typename?: 'WebContactEntityResponseCollection';
  data: Array<WebContactEntity>;
  meta: ResponseCollectionMeta;
};

export type WebContactRelationResponseCollection = {
  __typename?: 'WebContactRelationResponseCollection';
  data: Array<WebContactEntity>;
};

export type WebContactGroupFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  weight?: InputMaybe<IntFilterInput>;
  webContacts?: InputMaybe<WebContactFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  localizations?: InputMaybe<WebContactGroupFiltersInput>;
  locale?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<WebContactGroupFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebContactGroupFiltersInput>>>;
  not?: InputMaybe<WebContactGroupFiltersInput>;
};

export type WebContactGroupInput = {
  name?: InputMaybe<Scalars['String']>;
  weight?: InputMaybe<Scalars['Int']>;
  webContacts?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type WebContactGroup = {
  __typename?: 'WebContactGroup';
  name: Scalars['String'];
  weight: Scalars['Int'];
  webContacts?: Maybe<WebContactRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  localizations?: Maybe<WebContactGroupRelationResponseCollection>;
  locale?: Maybe<Scalars['String']>;
};


export type WebContactGroupWebContactsArgs = {
  filters?: InputMaybe<WebContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebContactGroupLocalizationsArgs = {
  filters?: InputMaybe<WebContactGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebContactGroupEntity = {
  __typename?: 'WebContactGroupEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<WebContactGroup>;
};

export type WebContactGroupEntityResponse = {
  __typename?: 'WebContactGroupEntityResponse';
  data?: Maybe<WebContactGroupEntity>;
};

export type WebContactGroupEntityResponseCollection = {
  __typename?: 'WebContactGroupEntityResponseCollection';
  data: Array<WebContactGroupEntity>;
  meta: ResponseCollectionMeta;
};

export type WebContactGroupRelationResponseCollection = {
  __typename?: 'WebContactGroupRelationResponseCollection';
  data: Array<WebContactGroupEntity>;
};

export enum Enum_Webmenuitem_Menutype {
  TopNav = 'top_nav',
  FrontPage = 'front_page',
  FooterNav = 'footer_nav'
}

export type WebMenuItemFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  text?: InputMaybe<StringFilterInput>;
  link?: InputMaybe<StringFilterInput>;
  isValid?: InputMaybe<BooleanFilterInput>;
  menuType?: InputMaybe<StringFilterInput>;
  weight?: InputMaybe<IntFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  localizations?: InputMaybe<WebMenuItemFiltersInput>;
  locale?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<WebMenuItemFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebMenuItemFiltersInput>>>;
  not?: InputMaybe<WebMenuItemFiltersInput>;
};

export type WebMenuItemInput = {
  text?: InputMaybe<Scalars['String']>;
  link?: InputMaybe<Scalars['String']>;
  isValid?: InputMaybe<Scalars['Boolean']>;
  menuType?: InputMaybe<Enum_Webmenuitem_Menutype>;
  weight?: InputMaybe<Scalars['Int']>;
};

export type WebMenuItem = {
  __typename?: 'WebMenuItem';
  text: Scalars['String'];
  link: Scalars['String'];
  isValid: Scalars['Boolean'];
  menuType: Enum_Webmenuitem_Menutype;
  weight: Scalars['Int'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  localizations?: Maybe<WebMenuItemRelationResponseCollection>;
  locale?: Maybe<Scalars['String']>;
};


export type WebMenuItemLocalizationsArgs = {
  filters?: InputMaybe<WebMenuItemFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebMenuItemEntity = {
  __typename?: 'WebMenuItemEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<WebMenuItem>;
};

export type WebMenuItemEntityResponse = {
  __typename?: 'WebMenuItemEntityResponse';
  data?: Maybe<WebMenuItemEntity>;
};

export type WebMenuItemEntityResponseCollection = {
  __typename?: 'WebMenuItemEntityResponseCollection';
  data: Array<WebMenuItemEntity>;
  meta: ResponseCollectionMeta;
};

export type WebMenuItemRelationResponseCollection = {
  __typename?: 'WebMenuItemRelationResponseCollection';
  data: Array<WebMenuItemEntity>;
};

export type WebPageBlocksDynamicZone = ComponentContentBlocksHomeSplashScreen | ComponentContentBlocksBasicContent | ComponentContentBlocksTimedContent | ComponentContentBlocksPartners | ComponentContentBlocksContacts | ComponentContentBlocksRegistrationForm | ComponentContentBlocksRegistrationSuccess | ComponentContentBlocksArticleList | Error;

export type WebPageFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  slug?: InputMaybe<StringFilterInput>;
  title?: InputMaybe<StringFilterInput>;
  noContentRedirectUrl?: InputMaybe<StringFilterInput>;
  metaDescription?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  localizations?: InputMaybe<WebPageFiltersInput>;
  locale?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<WebPageFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebPageFiltersInput>>>;
  not?: InputMaybe<WebPageFiltersInput>;
};

export type WebPageInput = {
  slug?: InputMaybe<Scalars['String']>;
  blocks?: InputMaybe<Array<Scalars['WebPageBlocksDynamicZoneInput']>>;
  title?: InputMaybe<Scalars['String']>;
  noContentRedirectUrl?: InputMaybe<Scalars['String']>;
  metaDescription?: InputMaybe<Scalars['String']>;
};

export type WebPage = {
  __typename?: 'WebPage';
  slug?: Maybe<Scalars['String']>;
  blocks?: Maybe<Array<Maybe<WebPageBlocksDynamicZone>>>;
  title?: Maybe<Scalars['String']>;
  noContentRedirectUrl?: Maybe<Scalars['String']>;
  metaDescription?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  localizations?: Maybe<WebPageRelationResponseCollection>;
  locale?: Maybe<Scalars['String']>;
};


export type WebPageLocalizationsArgs = {
  filters?: InputMaybe<WebPageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebPageEntity = {
  __typename?: 'WebPageEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<WebPage>;
};

export type WebPageEntityResponse = {
  __typename?: 'WebPageEntityResponse';
  data?: Maybe<WebPageEntity>;
};

export type WebPageEntityResponseCollection = {
  __typename?: 'WebPageEntityResponseCollection';
  data: Array<WebPageEntity>;
  meta: ResponseCollectionMeta;
};

export type WebPageRelationResponseCollection = {
  __typename?: 'WebPageRelationResponseCollection';
  data: Array<WebPageEntity>;
};

export type WebPartnerFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  url?: InputMaybe<StringFilterInput>;
  text?: InputMaybe<StringFilterInput>;
  webPartnerGroup?: InputMaybe<WebPartnerGroupFiltersInput>;
  weight?: InputMaybe<IntFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  localizations?: InputMaybe<WebPartnerFiltersInput>;
  locale?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<WebPartnerFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebPartnerFiltersInput>>>;
  not?: InputMaybe<WebPartnerFiltersInput>;
};

export type WebPartnerInput = {
  name?: InputMaybe<Scalars['String']>;
  url?: InputMaybe<Scalars['String']>;
  text?: InputMaybe<Scalars['String']>;
  logo?: InputMaybe<Scalars['ID']>;
  webPartnerGroup?: InputMaybe<Scalars['ID']>;
  weight?: InputMaybe<Scalars['Int']>;
};

export type WebPartner = {
  __typename?: 'WebPartner';
  name: Scalars['String'];
  url: Scalars['String'];
  text: Scalars['String'];
  logo: UploadFileEntityResponse;
  webPartnerGroup?: Maybe<WebPartnerGroupEntityResponse>;
  weight: Scalars['Int'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  localizations?: Maybe<WebPartnerRelationResponseCollection>;
  locale?: Maybe<Scalars['String']>;
};


export type WebPartnerLocalizationsArgs = {
  filters?: InputMaybe<WebPartnerFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebPartnerEntity = {
  __typename?: 'WebPartnerEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<WebPartner>;
};

export type WebPartnerEntityResponse = {
  __typename?: 'WebPartnerEntityResponse';
  data?: Maybe<WebPartnerEntity>;
};

export type WebPartnerEntityResponseCollection = {
  __typename?: 'WebPartnerEntityResponseCollection';
  data: Array<WebPartnerEntity>;
  meta: ResponseCollectionMeta;
};

export type WebPartnerRelationResponseCollection = {
  __typename?: 'WebPartnerRelationResponseCollection';
  data: Array<WebPartnerEntity>;
};

export enum Enum_Webpartnergroup_Importance {
  High = 'high',
  Medium = 'medium',
  Low = 'low'
}

export type WebPartnerGroupFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  weight?: InputMaybe<IntFilterInput>;
  importance?: InputMaybe<StringFilterInput>;
  webPartners?: InputMaybe<WebPartnerFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  localizations?: InputMaybe<WebPartnerGroupFiltersInput>;
  locale?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<WebPartnerGroupFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebPartnerGroupFiltersInput>>>;
  not?: InputMaybe<WebPartnerGroupFiltersInput>;
};

export type WebPartnerGroupInput = {
  name?: InputMaybe<Scalars['String']>;
  weight?: InputMaybe<Scalars['Int']>;
  importance?: InputMaybe<Enum_Webpartnergroup_Importance>;
  webPartners?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type WebPartnerGroup = {
  __typename?: 'WebPartnerGroup';
  name: Scalars['String'];
  weight: Scalars['Int'];
  importance: Enum_Webpartnergroup_Importance;
  webPartners?: Maybe<WebPartnerRelationResponseCollection>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  localizations?: Maybe<WebPartnerGroupRelationResponseCollection>;
  locale?: Maybe<Scalars['String']>;
};


export type WebPartnerGroupWebPartnersArgs = {
  filters?: InputMaybe<WebPartnerFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebPartnerGroupLocalizationsArgs = {
  filters?: InputMaybe<WebPartnerGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebPartnerGroupEntity = {
  __typename?: 'WebPartnerGroupEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<WebPartnerGroup>;
};

export type WebPartnerGroupEntityResponse = {
  __typename?: 'WebPartnerGroupEntityResponse';
  data?: Maybe<WebPartnerGroupEntity>;
};

export type WebPartnerGroupEntityResponseCollection = {
  __typename?: 'WebPartnerGroupEntityResponseCollection';
  data: Array<WebPartnerGroupEntity>;
  meta: ResponseCollectionMeta;
};

export type WebPartnerGroupRelationResponseCollection = {
  __typename?: 'WebPartnerGroupRelationResponseCollection';
  data: Array<WebPartnerGroupEntity>;
};

export enum Enum_Xotshirt_Size {
  S = 'S',
  M = 'M',
  L = 'L',
  Xl = 'XL',
  Xxl = 'XXL'
}

export enum Enum_Xotshirt_Color {
  Red = 'red',
  Blue = 'blue',
  Mix = 'mix'
}

export type XoTShirtFiltersInput = {
  id?: InputMaybe<IdFilterInput>;
  isAvailable?: InputMaybe<BooleanFilterInput>;
  size?: InputMaybe<StringFilterInput>;
  color?: InputMaybe<StringFilterInput>;
  pattern?: InputMaybe<StringFilterInput>;
  price?: InputMaybe<FloatFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<XoTShirtFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<XoTShirtFiltersInput>>>;
  not?: InputMaybe<XoTShirtFiltersInput>;
};

export type XoTShirtInput = {
  isAvailable?: InputMaybe<Scalars['Boolean']>;
  size?: InputMaybe<Enum_Xotshirt_Size>;
  color?: InputMaybe<Enum_Xotshirt_Color>;
  picture?: InputMaybe<Scalars['ID']>;
  pattern?: InputMaybe<Scalars['String']>;
  price?: InputMaybe<Scalars['Float']>;
};

export type XoTShirt = {
  __typename?: 'XoTShirt';
  isAvailable: Scalars['Boolean'];
  size: Enum_Xotshirt_Size;
  color: Enum_Xotshirt_Color;
  picture: UploadFileEntityResponse;
  pattern?: Maybe<Scalars['String']>;
  price: Scalars['Float'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type XoTShirtEntity = {
  __typename?: 'XoTShirtEntity';
  id?: Maybe<Scalars['ID']>;
  attributes?: Maybe<XoTShirt>;
};

export type XoTShirtEntityResponse = {
  __typename?: 'XoTShirtEntityResponse';
  data?: Maybe<XoTShirtEntity>;
};

export type XoTShirtEntityResponseCollection = {
  __typename?: 'XoTShirtEntityResponseCollection';
  data: Array<XoTShirtEntity>;
  meta: ResponseCollectionMeta;
};

export type GenericMorph = ComponentContentBlocksArticleList | ComponentContentBlocksBasicContent | ComponentContentBlocksContacts | ComponentContentBlocksHomeSplashScreen | ComponentContentBlocksPartners | ComponentContentBlocksRegistrationForm | ComponentContentBlocksRegistrationSuccess | ComponentContentBlocksTimedContent | ComponentDiscordDiscordIdList | ComponentMultimediaBlocksCroppedImage | ComponentMultimediaBlocksFormCheckField | ComponentTdAFisherPhasesInfoPanel | ComponentTdAFisherPhasesInfo | ComponentTdAFisherPhasesLink | ComponentTdaCtfComponentsEmail | ComponentTdaCtfComponentsGenericNote | ComponentTdaCtfComponentsStickyNote | UploadFile | UploadFolder | ContentReleasesRelease | ContentReleasesReleaseAction | I18NLocale | UsersPermissionsPermission | UsersPermissionsRole | UsersPermissionsUser | AmosGameStatistic | Contestant | Event | EventGroup | Project | ProjectType | Region | School | SchoolContact | SchoolType | TdaBot | TdaBotContestant | TdaBotTag | TdaCtfFile | TdaCtfFolder | TdaFisherInfoPanel | TdaFlag | TdaPhase | TdaSession | TdaTeam | TdaTeamFlag | TdaTeamTest | TdaTest | WebArticle | WebArticleCategory | WebContact | WebContactGroup | WebMenuItem | WebPage | WebPartner | WebPartnerGroup | XoTShirt;

export type FileInfoInput = {
  name?: InputMaybe<Scalars['String']>;
  alternativeText?: InputMaybe<Scalars['String']>;
  caption?: InputMaybe<Scalars['String']>;
};

export type UsersPermissionsMe = {
  __typename?: 'UsersPermissionsMe';
  id: Scalars['ID'];
  username: Scalars['String'];
  email?: Maybe<Scalars['String']>;
  confirmed?: Maybe<Scalars['Boolean']>;
  blocked?: Maybe<Scalars['Boolean']>;
  role?: Maybe<UsersPermissionsMeRole>;
};

export type UsersPermissionsMeRole = {
  __typename?: 'UsersPermissionsMeRole';
  id: Scalars['ID'];
  name: Scalars['String'];
  description?: Maybe<Scalars['String']>;
  type?: Maybe<Scalars['String']>;
};

export type UsersPermissionsRegisterInput = {
  username: Scalars['String'];
  email: Scalars['String'];
  password: Scalars['String'];
};

export type UsersPermissionsLoginInput = {
  identifier: Scalars['String'];
  password: Scalars['String'];
  provider?: Scalars['String'];
};

export type UsersPermissionsPasswordPayload = {
  __typename?: 'UsersPermissionsPasswordPayload';
  ok: Scalars['Boolean'];
};

export type UsersPermissionsLoginPayload = {
  __typename?: 'UsersPermissionsLoginPayload';
  jwt?: Maybe<Scalars['String']>;
  user: UsersPermissionsMe;
};

export type UsersPermissionsCreateRolePayload = {
  __typename?: 'UsersPermissionsCreateRolePayload';
  ok: Scalars['Boolean'];
};

export type UsersPermissionsUpdateRolePayload = {
  __typename?: 'UsersPermissionsUpdateRolePayload';
  ok: Scalars['Boolean'];
};

export type UsersPermissionsDeleteRolePayload = {
  __typename?: 'UsersPermissionsDeleteRolePayload';
  ok: Scalars['Boolean'];
};

export type PaginationArg = {
  page?: InputMaybe<Scalars['Int']>;
  pageSize?: InputMaybe<Scalars['Int']>;
  start?: InputMaybe<Scalars['Int']>;
  limit?: InputMaybe<Scalars['Int']>;
};

export type Query = {
  __typename?: 'Query';
  uploadFile?: Maybe<UploadFileEntityResponse>;
  uploadFiles?: Maybe<UploadFileEntityResponseCollection>;
  uploadFolder?: Maybe<UploadFolderEntityResponse>;
  uploadFolders?: Maybe<UploadFolderEntityResponseCollection>;
  contentReleasesRelease?: Maybe<ContentReleasesReleaseEntityResponse>;
  contentReleasesReleases?: Maybe<ContentReleasesReleaseEntityResponseCollection>;
  contentReleasesReleaseAction?: Maybe<ContentReleasesReleaseActionEntityResponse>;
  contentReleasesReleaseActions?: Maybe<ContentReleasesReleaseActionEntityResponseCollection>;
  i18NLocale?: Maybe<I18NLocaleEntityResponse>;
  i18NLocales?: Maybe<I18NLocaleEntityResponseCollection>;
  usersPermissionsRole?: Maybe<UsersPermissionsRoleEntityResponse>;
  usersPermissionsRoles?: Maybe<UsersPermissionsRoleEntityResponseCollection>;
  usersPermissionsUser?: Maybe<UsersPermissionsUserEntityResponse>;
  usersPermissionsUsers?: Maybe<UsersPermissionsUserEntityResponseCollection>;
  amosGameStatistic?: Maybe<AmosGameStatisticEntityResponse>;
  amosGameStatistics?: Maybe<AmosGameStatisticEntityResponseCollection>;
  contestant?: Maybe<ContestantEntityResponse>;
  contestants?: Maybe<ContestantEntityResponseCollection>;
  event?: Maybe<EventEntityResponse>;
  events?: Maybe<EventEntityResponseCollection>;
  eventGroup?: Maybe<EventGroupEntityResponse>;
  eventGroups?: Maybe<EventGroupEntityResponseCollection>;
  project?: Maybe<ProjectEntityResponse>;
  projects?: Maybe<ProjectEntityResponseCollection>;
  projectType?: Maybe<ProjectTypeEntityResponse>;
  projectTypes?: Maybe<ProjectTypeEntityResponseCollection>;
  region?: Maybe<RegionEntityResponse>;
  regions?: Maybe<RegionEntityResponseCollection>;
  school?: Maybe<SchoolEntityResponse>;
  schools?: Maybe<SchoolEntityResponseCollection>;
  schoolContact?: Maybe<SchoolContactEntityResponse>;
  schoolContacts?: Maybe<SchoolContactEntityResponseCollection>;
  schoolType?: Maybe<SchoolTypeEntityResponse>;
  schoolTypes?: Maybe<SchoolTypeEntityResponseCollection>;
  tdaBot?: Maybe<TdaBotEntityResponse>;
  tdaBotContestant?: Maybe<TdaBotContestantEntityResponse>;
  tdaBotContestants?: Maybe<TdaBotContestantEntityResponseCollection>;
  tdaBotTag?: Maybe<TdaBotTagEntityResponse>;
  tdaBotTags?: Maybe<TdaBotTagEntityResponseCollection>;
  tdaCtfFile?: Maybe<TdaCtfFileEntityResponse>;
  tdaCtfFiles?: Maybe<TdaCtfFileEntityResponseCollection>;
  tdaCtfFolder?: Maybe<TdaCtfFolderEntityResponse>;
  tdaCtfFolders?: Maybe<TdaCtfFolderEntityResponseCollection>;
  tdaFisherInfoPanel?: Maybe<TdaFisherInfoPanelEntityResponse>;
  tdaFlag?: Maybe<TdaFlagEntityResponse>;
  tdaFlags?: Maybe<TdaFlagEntityResponseCollection>;
  tdaPhase?: Maybe<TdaPhaseEntityResponse>;
  tdaPhases?: Maybe<TdaPhaseEntityResponseCollection>;
  tdaSession?: Maybe<TdaSessionEntityResponse>;
  tdaSessions?: Maybe<TdaSessionEntityResponseCollection>;
  tdaTeam?: Maybe<TdaTeamEntityResponse>;
  tdaTeams?: Maybe<TdaTeamEntityResponseCollection>;
  tdaTeamFlag?: Maybe<TdaTeamFlagEntityResponse>;
  tdaTeamFlags?: Maybe<TdaTeamFlagEntityResponseCollection>;
  tdaTeamTest?: Maybe<TdaTeamTestEntityResponse>;
  tdaTeamTests?: Maybe<TdaTeamTestEntityResponseCollection>;
  tdaTest?: Maybe<TdaTestEntityResponse>;
  tdaTests?: Maybe<TdaTestEntityResponseCollection>;
  webArticle?: Maybe<WebArticleEntityResponse>;
  webArticles?: Maybe<WebArticleEntityResponseCollection>;
  webArticleCategory?: Maybe<WebArticleCategoryEntityResponse>;
  webArticleCategories?: Maybe<WebArticleCategoryEntityResponseCollection>;
  webContact?: Maybe<WebContactEntityResponse>;
  webContacts?: Maybe<WebContactEntityResponseCollection>;
  webContactGroup?: Maybe<WebContactGroupEntityResponse>;
  webContactGroups?: Maybe<WebContactGroupEntityResponseCollection>;
  webMenuItem?: Maybe<WebMenuItemEntityResponse>;
  webMenuItems?: Maybe<WebMenuItemEntityResponseCollection>;
  webPage?: Maybe<WebPageEntityResponse>;
  webPages?: Maybe<WebPageEntityResponseCollection>;
  webPartner?: Maybe<WebPartnerEntityResponse>;
  webPartners?: Maybe<WebPartnerEntityResponseCollection>;
  webPartnerGroup?: Maybe<WebPartnerGroupEntityResponse>;
  webPartnerGroups?: Maybe<WebPartnerGroupEntityResponseCollection>;
  xoTShirt?: Maybe<XoTShirtEntityResponse>;
  xoTShirts?: Maybe<XoTShirtEntityResponseCollection>;
  me?: Maybe<UsersPermissionsMe>;
};


export type QueryUploadFileArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryUploadFilesArgs = {
  filters?: InputMaybe<UploadFileFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryUploadFolderArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryUploadFoldersArgs = {
  filters?: InputMaybe<UploadFolderFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryContentReleasesReleaseArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryContentReleasesReleasesArgs = {
  filters?: InputMaybe<ContentReleasesReleaseFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryContentReleasesReleaseActionArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryContentReleasesReleaseActionsArgs = {
  filters?: InputMaybe<ContentReleasesReleaseActionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryI18NLocaleArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryI18NLocalesArgs = {
  filters?: InputMaybe<I18NLocaleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryUsersPermissionsRoleArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryUsersPermissionsRolesArgs = {
  filters?: InputMaybe<UsersPermissionsRoleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryUsersPermissionsUserArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryUsersPermissionsUsersArgs = {
  filters?: InputMaybe<UsersPermissionsUserFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryAmosGameStatisticArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryAmosGameStatisticsArgs = {
  filters?: InputMaybe<AmosGameStatisticFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryContestantArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryContestantsArgs = {
  filters?: InputMaybe<ContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryEventArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryEventsArgs = {
  filters?: InputMaybe<EventFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryEventGroupArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryEventGroupsArgs = {
  filters?: InputMaybe<EventGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryProjectArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryProjectsArgs = {
  filters?: InputMaybe<ProjectFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryProjectTypeArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryProjectTypesArgs = {
  filters?: InputMaybe<ProjectTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryRegionArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryRegionsArgs = {
  filters?: InputMaybe<RegionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QuerySchoolArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QuerySchoolsArgs = {
  filters?: InputMaybe<SchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  publicationState?: InputMaybe<PublicationState>;
};


export type QuerySchoolContactArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QuerySchoolContactsArgs = {
  filters?: InputMaybe<SchoolContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QuerySchoolTypeArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QuerySchoolTypesArgs = {
  filters?: InputMaybe<SchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTdaBotArgs = {
  publicationState?: InputMaybe<PublicationState>;
};


export type QueryTdaBotContestantArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaBotContestantsArgs = {
  filters?: InputMaybe<TdaBotContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  publicationState?: InputMaybe<PublicationState>;
};


export type QueryTdaBotTagArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaBotTagsArgs = {
  filters?: InputMaybe<TdaBotTagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  publicationState?: InputMaybe<PublicationState>;
};


export type QueryTdaCtfFileArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaCtfFilesArgs = {
  filters?: InputMaybe<TdaCtfFileFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTdaCtfFolderArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaCtfFoldersArgs = {
  filters?: InputMaybe<TdaCtfFolderFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTdaFlagArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaFlagsArgs = {
  filters?: InputMaybe<TdaFlagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTdaPhaseArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaPhasesArgs = {
  filters?: InputMaybe<TdaPhaseFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTdaSessionArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaSessionsArgs = {
  filters?: InputMaybe<TdaSessionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTdaTeamArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaTeamsArgs = {
  filters?: InputMaybe<TdaTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTdaTeamFlagArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaTeamFlagsArgs = {
  filters?: InputMaybe<TdaTeamFlagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTdaTeamTestArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaTeamTestsArgs = {
  filters?: InputMaybe<TdaTeamTestFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTdaTestArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryTdaTestsArgs = {
  filters?: InputMaybe<TdaTestFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryWebArticleArgs = {
  id?: InputMaybe<Scalars['ID']>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebArticlesArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  publicationState?: InputMaybe<PublicationState>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebArticleCategoryArgs = {
  id?: InputMaybe<Scalars['ID']>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebArticleCategoriesArgs = {
  filters?: InputMaybe<WebArticleCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContactArgs = {
  id?: InputMaybe<Scalars['ID']>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContactsArgs = {
  filters?: InputMaybe<WebContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContactGroupArgs = {
  id?: InputMaybe<Scalars['ID']>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContactGroupsArgs = {
  filters?: InputMaybe<WebContactGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebMenuItemArgs = {
  id?: InputMaybe<Scalars['ID']>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebMenuItemsArgs = {
  filters?: InputMaybe<WebMenuItemFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPageArgs = {
  id?: InputMaybe<Scalars['ID']>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPagesArgs = {
  filters?: InputMaybe<WebPageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartnerArgs = {
  id?: InputMaybe<Scalars['ID']>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartnersArgs = {
  filters?: InputMaybe<WebPartnerFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartnerGroupArgs = {
  id?: InputMaybe<Scalars['ID']>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartnerGroupsArgs = {
  filters?: InputMaybe<WebPartnerGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryXoTShirtArgs = {
  id?: InputMaybe<Scalars['ID']>;
};


export type QueryXoTShirtsArgs = {
  filters?: InputMaybe<XoTShirtFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type Mutation = {
  __typename?: 'Mutation';
  createUploadFile?: Maybe<UploadFileEntityResponse>;
  updateUploadFile?: Maybe<UploadFileEntityResponse>;
  deleteUploadFile?: Maybe<UploadFileEntityResponse>;
  createUploadFolder?: Maybe<UploadFolderEntityResponse>;
  updateUploadFolder?: Maybe<UploadFolderEntityResponse>;
  deleteUploadFolder?: Maybe<UploadFolderEntityResponse>;
  createContentReleasesRelease?: Maybe<ContentReleasesReleaseEntityResponse>;
  updateContentReleasesRelease?: Maybe<ContentReleasesReleaseEntityResponse>;
  deleteContentReleasesRelease?: Maybe<ContentReleasesReleaseEntityResponse>;
  createContentReleasesReleaseAction?: Maybe<ContentReleasesReleaseActionEntityResponse>;
  updateContentReleasesReleaseAction?: Maybe<ContentReleasesReleaseActionEntityResponse>;
  deleteContentReleasesReleaseAction?: Maybe<ContentReleasesReleaseActionEntityResponse>;
  createAmosGameStatistic?: Maybe<AmosGameStatisticEntityResponse>;
  updateAmosGameStatistic?: Maybe<AmosGameStatisticEntityResponse>;
  deleteAmosGameStatistic?: Maybe<AmosGameStatisticEntityResponse>;
  createContestant?: Maybe<ContestantEntityResponse>;
  updateContestant?: Maybe<ContestantEntityResponse>;
  deleteContestant?: Maybe<ContestantEntityResponse>;
  createEvent?: Maybe<EventEntityResponse>;
  updateEvent?: Maybe<EventEntityResponse>;
  deleteEvent?: Maybe<EventEntityResponse>;
  createEventGroup?: Maybe<EventGroupEntityResponse>;
  updateEventGroup?: Maybe<EventGroupEntityResponse>;
  deleteEventGroup?: Maybe<EventGroupEntityResponse>;
  createProject?: Maybe<ProjectEntityResponse>;
  updateProject?: Maybe<ProjectEntityResponse>;
  deleteProject?: Maybe<ProjectEntityResponse>;
  createProjectType?: Maybe<ProjectTypeEntityResponse>;
  updateProjectType?: Maybe<ProjectTypeEntityResponse>;
  deleteProjectType?: Maybe<ProjectTypeEntityResponse>;
  createRegion?: Maybe<RegionEntityResponse>;
  updateRegion?: Maybe<RegionEntityResponse>;
  deleteRegion?: Maybe<RegionEntityResponse>;
  createSchool?: Maybe<SchoolEntityResponse>;
  updateSchool?: Maybe<SchoolEntityResponse>;
  deleteSchool?: Maybe<SchoolEntityResponse>;
  createSchoolContact?: Maybe<SchoolContactEntityResponse>;
  updateSchoolContact?: Maybe<SchoolContactEntityResponse>;
  deleteSchoolContact?: Maybe<SchoolContactEntityResponse>;
  createSchoolType?: Maybe<SchoolTypeEntityResponse>;
  updateSchoolType?: Maybe<SchoolTypeEntityResponse>;
  deleteSchoolType?: Maybe<SchoolTypeEntityResponse>;
  updateTdaBot?: Maybe<TdaBotEntityResponse>;
  deleteTdaBot?: Maybe<TdaBotEntityResponse>;
  createTdaBotContestant?: Maybe<TdaBotContestantEntityResponse>;
  updateTdaBotContestant?: Maybe<TdaBotContestantEntityResponse>;
  deleteTdaBotContestant?: Maybe<TdaBotContestantEntityResponse>;
  createTdaBotTag?: Maybe<TdaBotTagEntityResponse>;
  updateTdaBotTag?: Maybe<TdaBotTagEntityResponse>;
  deleteTdaBotTag?: Maybe<TdaBotTagEntityResponse>;
  createTdaCtfFile?: Maybe<TdaCtfFileEntityResponse>;
  updateTdaCtfFile?: Maybe<TdaCtfFileEntityResponse>;
  deleteTdaCtfFile?: Maybe<TdaCtfFileEntityResponse>;
  createTdaCtfFolder?: Maybe<TdaCtfFolderEntityResponse>;
  updateTdaCtfFolder?: Maybe<TdaCtfFolderEntityResponse>;
  deleteTdaCtfFolder?: Maybe<TdaCtfFolderEntityResponse>;
  updateTdaFisherInfoPanel?: Maybe<TdaFisherInfoPanelEntityResponse>;
  deleteTdaFisherInfoPanel?: Maybe<TdaFisherInfoPanelEntityResponse>;
  createTdaFlag?: Maybe<TdaFlagEntityResponse>;
  updateTdaFlag?: Maybe<TdaFlagEntityResponse>;
  deleteTdaFlag?: Maybe<TdaFlagEntityResponse>;
  createTdaPhase?: Maybe<TdaPhaseEntityResponse>;
  updateTdaPhase?: Maybe<TdaPhaseEntityResponse>;
  deleteTdaPhase?: Maybe<TdaPhaseEntityResponse>;
  createTdaSession?: Maybe<TdaSessionEntityResponse>;
  updateTdaSession?: Maybe<TdaSessionEntityResponse>;
  deleteTdaSession?: Maybe<TdaSessionEntityResponse>;
  createTdaTeam?: Maybe<TdaTeamEntityResponse>;
  updateTdaTeam?: Maybe<TdaTeamEntityResponse>;
  deleteTdaTeam?: Maybe<TdaTeamEntityResponse>;
  createTdaTeamFlag?: Maybe<TdaTeamFlagEntityResponse>;
  updateTdaTeamFlag?: Maybe<TdaTeamFlagEntityResponse>;
  deleteTdaTeamFlag?: Maybe<TdaTeamFlagEntityResponse>;
  createTdaTeamTest?: Maybe<TdaTeamTestEntityResponse>;
  updateTdaTeamTest?: Maybe<TdaTeamTestEntityResponse>;
  deleteTdaTeamTest?: Maybe<TdaTeamTestEntityResponse>;
  createTdaTest?: Maybe<TdaTestEntityResponse>;
  updateTdaTest?: Maybe<TdaTestEntityResponse>;
  deleteTdaTest?: Maybe<TdaTestEntityResponse>;
  createWebArticle?: Maybe<WebArticleEntityResponse>;
  updateWebArticle?: Maybe<WebArticleEntityResponse>;
  deleteWebArticle?: Maybe<WebArticleEntityResponse>;
  createWebArticleCategory?: Maybe<WebArticleCategoryEntityResponse>;
  updateWebArticleCategory?: Maybe<WebArticleCategoryEntityResponse>;
  deleteWebArticleCategory?: Maybe<WebArticleCategoryEntityResponse>;
  createWebContact?: Maybe<WebContactEntityResponse>;
  updateWebContact?: Maybe<WebContactEntityResponse>;
  deleteWebContact?: Maybe<WebContactEntityResponse>;
  createWebContactGroup?: Maybe<WebContactGroupEntityResponse>;
  updateWebContactGroup?: Maybe<WebContactGroupEntityResponse>;
  deleteWebContactGroup?: Maybe<WebContactGroupEntityResponse>;
  createWebMenuItem?: Maybe<WebMenuItemEntityResponse>;
  updateWebMenuItem?: Maybe<WebMenuItemEntityResponse>;
  deleteWebMenuItem?: Maybe<WebMenuItemEntityResponse>;
  createWebPage?: Maybe<WebPageEntityResponse>;
  updateWebPage?: Maybe<WebPageEntityResponse>;
  deleteWebPage?: Maybe<WebPageEntityResponse>;
  createWebPartner?: Maybe<WebPartnerEntityResponse>;
  updateWebPartner?: Maybe<WebPartnerEntityResponse>;
  deleteWebPartner?: Maybe<WebPartnerEntityResponse>;
  createWebPartnerGroup?: Maybe<WebPartnerGroupEntityResponse>;
  updateWebPartnerGroup?: Maybe<WebPartnerGroupEntityResponse>;
  deleteWebPartnerGroup?: Maybe<WebPartnerGroupEntityResponse>;
  createXoTShirt?: Maybe<XoTShirtEntityResponse>;
  updateXoTShirt?: Maybe<XoTShirtEntityResponse>;
  deleteXoTShirt?: Maybe<XoTShirtEntityResponse>;
  upload: UploadFileEntityResponse;
  multipleUpload: Array<Maybe<UploadFileEntityResponse>>;
  updateFileInfo: UploadFileEntityResponse;
  removeFile?: Maybe<UploadFileEntityResponse>;
  createWebArticleLocalization?: Maybe<WebArticleEntityResponse>;
  createWebArticleCategoryLocalization?: Maybe<WebArticleCategoryEntityResponse>;
  createWebContactLocalization?: Maybe<WebContactEntityResponse>;
  createWebContactGroupLocalization?: Maybe<WebContactGroupEntityResponse>;
  createWebMenuItemLocalization?: Maybe<WebMenuItemEntityResponse>;
  createWebPageLocalization?: Maybe<WebPageEntityResponse>;
  createWebPartnerLocalization?: Maybe<WebPartnerEntityResponse>;
  createWebPartnerGroupLocalization?: Maybe<WebPartnerGroupEntityResponse>;
  /** Create a new role */
  createUsersPermissionsRole?: Maybe<UsersPermissionsCreateRolePayload>;
  /** Update an existing role */
  updateUsersPermissionsRole?: Maybe<UsersPermissionsUpdateRolePayload>;
  /** Delete an existing role */
  deleteUsersPermissionsRole?: Maybe<UsersPermissionsDeleteRolePayload>;
  /** Create a new user */
  createUsersPermissionsUser: UsersPermissionsUserEntityResponse;
  /** Update an existing user */
  updateUsersPermissionsUser: UsersPermissionsUserEntityResponse;
  /** Delete an existing user */
  deleteUsersPermissionsUser: UsersPermissionsUserEntityResponse;
  login: UsersPermissionsLoginPayload;
  /** Register a user */
  register: UsersPermissionsLoginPayload;
  /** Request a reset password token */
  forgotPassword?: Maybe<UsersPermissionsPasswordPayload>;
  /** Reset user password. Confirm with a code (resetToken from forgotPassword) */
  resetPassword?: Maybe<UsersPermissionsLoginPayload>;
  /** Change user password. Confirm with the current password. */
  changePassword?: Maybe<UsersPermissionsLoginPayload>;
  /** Confirm an email users email address */
  emailConfirmation?: Maybe<UsersPermissionsLoginPayload>;
};


export type MutationCreateUploadFileArgs = {
  data: UploadFileInput;
};


export type MutationUpdateUploadFileArgs = {
  id: Scalars['ID'];
  data: UploadFileInput;
};


export type MutationDeleteUploadFileArgs = {
  id: Scalars['ID'];
};


export type MutationCreateUploadFolderArgs = {
  data: UploadFolderInput;
};


export type MutationUpdateUploadFolderArgs = {
  id: Scalars['ID'];
  data: UploadFolderInput;
};


export type MutationDeleteUploadFolderArgs = {
  id: Scalars['ID'];
};


export type MutationCreateContentReleasesReleaseArgs = {
  data: ContentReleasesReleaseInput;
};


export type MutationUpdateContentReleasesReleaseArgs = {
  id: Scalars['ID'];
  data: ContentReleasesReleaseInput;
};


export type MutationDeleteContentReleasesReleaseArgs = {
  id: Scalars['ID'];
};


export type MutationCreateContentReleasesReleaseActionArgs = {
  data: ContentReleasesReleaseActionInput;
};


export type MutationUpdateContentReleasesReleaseActionArgs = {
  id: Scalars['ID'];
  data: ContentReleasesReleaseActionInput;
};


export type MutationDeleteContentReleasesReleaseActionArgs = {
  id: Scalars['ID'];
};


export type MutationCreateAmosGameStatisticArgs = {
  data: AmosGameStatisticInput;
};


export type MutationUpdateAmosGameStatisticArgs = {
  id: Scalars['ID'];
  data: AmosGameStatisticInput;
};


export type MutationDeleteAmosGameStatisticArgs = {
  id: Scalars['ID'];
};


export type MutationCreateContestantArgs = {
  data: ContestantInput;
};


export type MutationUpdateContestantArgs = {
  id: Scalars['ID'];
  data: ContestantInput;
};


export type MutationDeleteContestantArgs = {
  id: Scalars['ID'];
};


export type MutationCreateEventArgs = {
  data: EventInput;
};


export type MutationUpdateEventArgs = {
  id: Scalars['ID'];
  data: EventInput;
};


export type MutationDeleteEventArgs = {
  id: Scalars['ID'];
};


export type MutationCreateEventGroupArgs = {
  data: EventGroupInput;
};


export type MutationUpdateEventGroupArgs = {
  id: Scalars['ID'];
  data: EventGroupInput;
};


export type MutationDeleteEventGroupArgs = {
  id: Scalars['ID'];
};


export type MutationCreateProjectArgs = {
  data: ProjectInput;
};


export type MutationUpdateProjectArgs = {
  id: Scalars['ID'];
  data: ProjectInput;
};


export type MutationDeleteProjectArgs = {
  id: Scalars['ID'];
};


export type MutationCreateProjectTypeArgs = {
  data: ProjectTypeInput;
};


export type MutationUpdateProjectTypeArgs = {
  id: Scalars['ID'];
  data: ProjectTypeInput;
};


export type MutationDeleteProjectTypeArgs = {
  id: Scalars['ID'];
};


export type MutationCreateRegionArgs = {
  data: RegionInput;
};


export type MutationUpdateRegionArgs = {
  id: Scalars['ID'];
  data: RegionInput;
};


export type MutationDeleteRegionArgs = {
  id: Scalars['ID'];
};


export type MutationCreateSchoolArgs = {
  data: SchoolInput;
};


export type MutationUpdateSchoolArgs = {
  id: Scalars['ID'];
  data: SchoolInput;
};


export type MutationDeleteSchoolArgs = {
  id: Scalars['ID'];
};


export type MutationCreateSchoolContactArgs = {
  data: SchoolContactInput;
};


export type MutationUpdateSchoolContactArgs = {
  id: Scalars['ID'];
  data: SchoolContactInput;
};


export type MutationDeleteSchoolContactArgs = {
  id: Scalars['ID'];
};


export type MutationCreateSchoolTypeArgs = {
  data: SchoolTypeInput;
};


export type MutationUpdateSchoolTypeArgs = {
  id: Scalars['ID'];
  data: SchoolTypeInput;
};


export type MutationDeleteSchoolTypeArgs = {
  id: Scalars['ID'];
};


export type MutationUpdateTdaBotArgs = {
  data: TdaBotInput;
};


export type MutationCreateTdaBotContestantArgs = {
  data: TdaBotContestantInput;
};


export type MutationUpdateTdaBotContestantArgs = {
  id: Scalars['ID'];
  data: TdaBotContestantInput;
};


export type MutationDeleteTdaBotContestantArgs = {
  id: Scalars['ID'];
};


export type MutationCreateTdaBotTagArgs = {
  data: TdaBotTagInput;
};


export type MutationUpdateTdaBotTagArgs = {
  id: Scalars['ID'];
  data: TdaBotTagInput;
};


export type MutationDeleteTdaBotTagArgs = {
  id: Scalars['ID'];
};


export type MutationCreateTdaCtfFileArgs = {
  data: TdaCtfFileInput;
};


export type MutationUpdateTdaCtfFileArgs = {
  id: Scalars['ID'];
  data: TdaCtfFileInput;
};


export type MutationDeleteTdaCtfFileArgs = {
  id: Scalars['ID'];
};


export type MutationCreateTdaCtfFolderArgs = {
  data: TdaCtfFolderInput;
};


export type MutationUpdateTdaCtfFolderArgs = {
  id: Scalars['ID'];
  data: TdaCtfFolderInput;
};


export type MutationDeleteTdaCtfFolderArgs = {
  id: Scalars['ID'];
};


export type MutationUpdateTdaFisherInfoPanelArgs = {
  data: TdaFisherInfoPanelInput;
};


export type MutationCreateTdaFlagArgs = {
  data: TdaFlagInput;
};


export type MutationUpdateTdaFlagArgs = {
  id: Scalars['ID'];
  data: TdaFlagInput;
};


export type MutationDeleteTdaFlagArgs = {
  id: Scalars['ID'];
};


export type MutationCreateTdaPhaseArgs = {
  data: TdaPhaseInput;
};


export type MutationUpdateTdaPhaseArgs = {
  id: Scalars['ID'];
  data: TdaPhaseInput;
};


export type MutationDeleteTdaPhaseArgs = {
  id: Scalars['ID'];
};


export type MutationCreateTdaSessionArgs = {
  data: TdaSessionInput;
};


export type MutationUpdateTdaSessionArgs = {
  id: Scalars['ID'];
  data: TdaSessionInput;
};


export type MutationDeleteTdaSessionArgs = {
  id: Scalars['ID'];
};


export type MutationCreateTdaTeamArgs = {
  data: TdaTeamInput;
};


export type MutationUpdateTdaTeamArgs = {
  id: Scalars['ID'];
  data: TdaTeamInput;
};


export type MutationDeleteTdaTeamArgs = {
  id: Scalars['ID'];
};


export type MutationCreateTdaTeamFlagArgs = {
  data: TdaTeamFlagInput;
};


export type MutationUpdateTdaTeamFlagArgs = {
  id: Scalars['ID'];
  data: TdaTeamFlagInput;
};


export type MutationDeleteTdaTeamFlagArgs = {
  id: Scalars['ID'];
};


export type MutationCreateTdaTeamTestArgs = {
  data: TdaTeamTestInput;
};


export type MutationUpdateTdaTeamTestArgs = {
  id: Scalars['ID'];
  data: TdaTeamTestInput;
};


export type MutationDeleteTdaTeamTestArgs = {
  id: Scalars['ID'];
};


export type MutationCreateTdaTestArgs = {
  data: TdaTestInput;
};


export type MutationUpdateTdaTestArgs = {
  id: Scalars['ID'];
  data: TdaTestInput;
};


export type MutationDeleteTdaTestArgs = {
  id: Scalars['ID'];
};


export type MutationCreateWebArticleArgs = {
  data: WebArticleInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebArticleArgs = {
  id: Scalars['ID'];
  data: WebArticleInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebArticleArgs = {
  id: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebArticleCategoryArgs = {
  data: WebArticleCategoryInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebArticleCategoryArgs = {
  id: Scalars['ID'];
  data: WebArticleCategoryInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebArticleCategoryArgs = {
  id: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebContactArgs = {
  data: WebContactInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebContactArgs = {
  id: Scalars['ID'];
  data: WebContactInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebContactArgs = {
  id: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebContactGroupArgs = {
  data: WebContactGroupInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebContactGroupArgs = {
  id: Scalars['ID'];
  data: WebContactGroupInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebContactGroupArgs = {
  id: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebMenuItemArgs = {
  data: WebMenuItemInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebMenuItemArgs = {
  id: Scalars['ID'];
  data: WebMenuItemInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebMenuItemArgs = {
  id: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebPageArgs = {
  data: WebPageInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebPageArgs = {
  id: Scalars['ID'];
  data: WebPageInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebPageArgs = {
  id: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebPartnerArgs = {
  data: WebPartnerInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebPartnerArgs = {
  id: Scalars['ID'];
  data: WebPartnerInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebPartnerArgs = {
  id: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebPartnerGroupArgs = {
  data: WebPartnerGroupInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebPartnerGroupArgs = {
  id: Scalars['ID'];
  data: WebPartnerGroupInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebPartnerGroupArgs = {
  id: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateXoTShirtArgs = {
  data: XoTShirtInput;
};


export type MutationUpdateXoTShirtArgs = {
  id: Scalars['ID'];
  data: XoTShirtInput;
};


export type MutationDeleteXoTShirtArgs = {
  id: Scalars['ID'];
};


export type MutationUploadArgs = {
  refId?: InputMaybe<Scalars['ID']>;
  ref?: InputMaybe<Scalars['String']>;
  field?: InputMaybe<Scalars['String']>;
  info?: InputMaybe<FileInfoInput>;
  file: Scalars['Upload'];
};


export type MutationMultipleUploadArgs = {
  refId?: InputMaybe<Scalars['ID']>;
  ref?: InputMaybe<Scalars['String']>;
  field?: InputMaybe<Scalars['String']>;
  files: Array<InputMaybe<Scalars['Upload']>>;
};


export type MutationUpdateFileInfoArgs = {
  id: Scalars['ID'];
  info?: InputMaybe<FileInfoInput>;
};


export type MutationRemoveFileArgs = {
  id: Scalars['ID'];
};


export type MutationCreateWebArticleLocalizationArgs = {
  id?: InputMaybe<Scalars['ID']>;
  data?: InputMaybe<WebArticleInput>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebArticleCategoryLocalizationArgs = {
  id?: InputMaybe<Scalars['ID']>;
  data?: InputMaybe<WebArticleCategoryInput>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebContactLocalizationArgs = {
  id?: InputMaybe<Scalars['ID']>;
  data?: InputMaybe<WebContactInput>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebContactGroupLocalizationArgs = {
  id?: InputMaybe<Scalars['ID']>;
  data?: InputMaybe<WebContactGroupInput>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebMenuItemLocalizationArgs = {
  id?: InputMaybe<Scalars['ID']>;
  data?: InputMaybe<WebMenuItemInput>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebPageLocalizationArgs = {
  id?: InputMaybe<Scalars['ID']>;
  data?: InputMaybe<WebPageInput>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebPartnerLocalizationArgs = {
  id?: InputMaybe<Scalars['ID']>;
  data?: InputMaybe<WebPartnerInput>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebPartnerGroupLocalizationArgs = {
  id?: InputMaybe<Scalars['ID']>;
  data?: InputMaybe<WebPartnerGroupInput>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateUsersPermissionsRoleArgs = {
  data: UsersPermissionsRoleInput;
};


export type MutationUpdateUsersPermissionsRoleArgs = {
  id: Scalars['ID'];
  data: UsersPermissionsRoleInput;
};


export type MutationDeleteUsersPermissionsRoleArgs = {
  id: Scalars['ID'];
};


export type MutationCreateUsersPermissionsUserArgs = {
  data: UsersPermissionsUserInput;
};


export type MutationUpdateUsersPermissionsUserArgs = {
  id: Scalars['ID'];
  data: UsersPermissionsUserInput;
};


export type MutationDeleteUsersPermissionsUserArgs = {
  id: Scalars['ID'];
};


export type MutationLoginArgs = {
  input: UsersPermissionsLoginInput;
};


export type MutationRegisterArgs = {
  input: UsersPermissionsRegisterInput;
};


export type MutationForgotPasswordArgs = {
  email: Scalars['String'];
};


export type MutationResetPasswordArgs = {
  password: Scalars['String'];
  passwordConfirmation: Scalars['String'];
  code: Scalars['String'];
};


export type MutationChangePasswordArgs = {
  currentPassword: Scalars['String'];
  password: Scalars['String'];
  passwordConfirmation: Scalars['String'];
};


export type MutationEmailConfirmationArgs = {
  confirmation: Scalars['String'];
};
