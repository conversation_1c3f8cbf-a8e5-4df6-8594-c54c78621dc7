import { stringify } from 'qs';
import { getBaseUrl } from "@/utils/dataUtils";
const { FISHBUSH_API_KEY } = useRuntimeConfig().public;

export default defineEventHandler(async () => {
  const baseUrl = getBaseUrl();
  const locales = ['cs', 'en']; // The first locale is the default one, omit it from the sitemap URL prefix

  const sitemapEntries = [];

  for (const locale of locales) {
    const fullUrl = new URL('api/web-pages', baseUrl);

    const queryParams = {
      fields: ['slug', 'updatedAt'],
      locale, // Use the current locale in the loop
    };

    const queryString = stringify(queryParams, { encodeValuesOnly: true });
    fullUrl.search = `?${queryString}`;

    try {
      const response = await fetch(fullUrl.href, {
        headers: {
          Authorization: `Bearer ${FISHBUSH_API_KEY}`,
        },
      });

      if (!response.ok) {
        console.error(`Failed to fetch pages for locale ${locale}:`, response.statusText);
        continue;
      }

      const data: WebPageEntityResponseCollection = await response.json();

      for (const page of data.data) {
        // Include the locale in the URL and strip the first slash from the page name if it exists
        
        const loc = (locale === locales[0]) ? `/${page.attributes?.slug?.replace(/^\//, '')}` : `/${locale}/${page.attributes?.slug?.replace(/^\//, '')}`
        sitemapEntries.push({
          loc,
          lastmod: new Date(page.attributes?.updatedAt).toISOString(),
          changefreq: 'daily',
          priority: 0.8,
        });
      }
    } catch (error) {
      console.error(`Error fetching pages for locale ${locale}:`, error);
    }
  }

  return sitemapEntries;
});
