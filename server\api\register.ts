import {
	type ContestantEntityResponseCollection,
	Enum_Contestant_Role,
} from "@/models/types";
import type { App } from "@tour-de-app/authfish";
import { defineEventHandler } from "h3";
import { hc } from "hono/client";
import { stringify } from "qs";
import { v4 as uuidv4 } from "uuid";
import { getBaseUrl } from "@/utils/dataUtils";
const { FISHBUSH_API_KEY_TRUSTED, KEYCLOAK_API_TOKEN } = useRuntimeConfig();

const config = useRuntimeConfig();
const client = hc<App>(config.KEYCLOAK_API_URL);

function generateReferralCode(dutriplets: number): string {
	function getRandomElement(arr: string[]): string {
		return arr[Math.floor(Math.random() * arr.length)];
	}
	const vowels = ["A", "E", "I", "O", "U", "AU", "OU", "EU"];
	const consonants = [
		"B",
		"C",
		"D",
		"F",
		"G",
		"H",
		"J",
		"K",
		"L",
		"M",
		"N",
		"P",
		"Q",
		"R",
		"S",
		"T",
		"V",
		"W",
		"X",
		"Z",
	];
	let referralCode = "";

	for (let i = 0; i < dutriplets; i++) {
		referralCode += getRandomElement(consonants);
		referralCode += getRandomElement(vowels);
	}
	referralCode += getRandomElement(consonants);

	return referralCode;
}

interface FBids {
	teacherId: number;
	contestantIds: number[];
	teamId: number;
}

interface KCids {
	teamId: string;
	contestantIds: string[];
}

interface Person {
	firstname: string;
	lastname: string;
	email: string;
	phone: string;
}

interface Invoice {
	name: string;
	email: string;
	street: string;
	city: string;
	zip: string;
	ic: string;
	dic: string;
}

interface Registration {
	teamName: string;
	school: SchoolEntity;
	referralCode: string;
	teacher: Person;
	contestants: Person[];
	contestantCount: number;
	hasCaptain: boolean;
	allowedPartnerDataAccess: boolean;
	invoice?: Invoice;
}

async function sendToKeycloak(
	data: Registration,
	newUUID: string,
): Promise<KCids> {
	const response = await client.users.$post(
		{
			json: {
				team: {
					name: data.teamName,
					id: newUUID,
				},
				users: data.contestants
					.filter(
						(contestant) =>
							contestant.firstname && contestant.lastname && contestant.email,
					)
					.map((contestant) => ({
						email: contestant.email,
						firstName: contestant.firstname,
						lastName: contestant.lastname,
						temporaryPassword: uuidv4(),
					})),
			},
		},
		{
			headers: {
				"Content-Type": "application/json",
				"X-Api-Key": KEYCLOAK_API_TOKEN,
			},
		},
	);

	const resdata = await response.json();

	if (!response.ok) {
		throw new Error(
			`Failed to register team to Keycloak: ${response.statusText}`,
		);
	}

	if (response.status === 201 && "users" in resdata && "team" in resdata) {
		const users: string[] = resdata.users as string[]; // Access the users array
		const team: string = resdata.team as string; // Access the team name
		return { teamId: team, contestantIds: users };
	}

	throw new Error(`Unexpected response format: ${JSON.stringify(resdata)}`);
}

async function rollbackKeycloakApi(KCids: KCids) {
	await client.users.rollback.$post(
		{
			json: {
				team: KCids.teamId,
				users: KCids.contestantIds,
			},
		},
		{
			headers: {
				"Content-Type": "application/json",
				"X-Api-Key": KEYCLOAK_API_TOKEN,
			},
		},
	);
}

async function createOrFindTeacherContact(
	teacher: Person,
	schoolId: number,
	baseUrl: string,
): Promise<number> {
	const url = new URL("api/school-contacts", baseUrl);

	// step 1: try to find the teacher contact
	const queryParams = {
		fields: ["id"],
		filters: {
			email: teacher.email,
			school: schoolId.toString(),
		},
	};

	const queryString = stringify(queryParams, { encodeValuesOnly: true });
	url.search = `?${queryString}`;

	const responseGet = await fetch(url.href, {
		headers: {
			Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
		},
	});

	if (!responseGet.ok) {
		throw new Error(`Failed to create teacher contact: ${responseGet.statusText}`);
	}
	const dataGet = await responseGet.json();
	if (dataGet.data.length > 0) {
		return dataGet.data[0].id;
	}

	// optional step 2: create the teacher contact if not found
	const requestBody: MutationCreateSchoolContactArgs = {
		data: {
			firstname: teacher.firstname,
			lastname: teacher.lastname,
			email: teacher.email,
			phoneNumber: teacher.phone,
			school: schoolId.toString(),
		},
	};

	const responsePost = await fetch(url.href, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
		},
		body: JSON.stringify(requestBody),
	});

	if (!responsePost.ok) {
		throw new Error(`Failed to create teacher contact: ${responsePost.statusText}`);
	}
	const data = await responsePost.json();
	return data.data.id;
}

async function getContestantDuplicates(emails: string[]): Promise<string[]> {
	const baseUrl = getBaseUrl();
	const url = new URL("api/contestants", baseUrl);
	const queryParams = {
		fields: ["email"],
		filters: {
			email: {
				$in: emails,
			},
		}
	};

	const queryString = stringify(queryParams, { encodeValuesOnly: true });
	url.search = `?${queryString}`;

	const response = await fetch(url.href, {
		headers: {
			Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
		},
	});

	if (!response.ok) {
		throw new Error(`Failed to create contestant: ${response.statusText}`);
	}

	const data: ContestantEntityResponseCollection = await response.json();
	const duplicateEmails = data.data.map(
		(contestant) => contestant.attributes?.email ?? "",
	);
	const duplicateOriginalEmails = emails.filter(
		(email, index) => emails.indexOf(email) !== index,
	);
	const uniqueEmails = [
		...new Set([...duplicateEmails, ...duplicateOriginalEmails]),
	].filter((email) => email !== "");
	return uniqueEmails;
}

async function createContestant(
	contestant: Person,
	schoolId: number,
	role: Enum_Contestant_Role,
	baseUrl: string,
): Promise<number> {
	const url = new URL("api/contestants", baseUrl);
	const requestBody: MutationCreateContestantArgs = {
		data: {
			firstname: contestant.firstname,
			lastname: contestant.lastname,
			email: `${contestant.email}`,
			phoneNumber: contestant.phone,
			school: schoolId.toString(),
			role: role,
		},
	};

	const response = await fetch(url.href, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
		},
		body: JSON.stringify(requestBody),
	});

	if (!response.ok) {
		throw new Error(`Failed to create contestant: ${response.statusText}`);
	}
	const data = await response.json();
	return data.data.id;
}

async function createTeam(
	teamName: string,
	teacherId: number,
	contestantIds: number[],
	ownReferral: string,
	usedReferral: string | null,
	allowedPartnerDataAccess: boolean,
	newUUID: string,
	baseUrl: string,
): Promise<number> {
	//name, access = true, uuid generuju, ownReferralCode generuju, usedReferral dostávám, schoolContact = teacher, contestants = contestants, allowedPartnewrDataAccess z ofrmuláře, discordCode generuju uuid
	const url = new URL("api/tda-teams", baseUrl);

	const requestBody: MutationCreateTdaTeamArgs = {
		data: {
			name: teamName,
			//url
			access: true,
			uuid: newUUID,
			//ghostID
			//subdomain
			ownReferral: ownReferral,
			usedReferral: usedReferral,
			schoolContact: teacherId.toString(),
			contestants: contestantIds.map((id) => id.toString()),
			allowedPartnerDataAccess: allowedPartnerDataAccess,
			discordCode: uuidv4(),
		},
	};

	const response = await fetch(url.href, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
		},
		body: JSON.stringify(requestBody),
	});

	if (!response.ok) {
		throw new Error(`Failed to create contestant: ${response.statusText}`);
	}
	const data = await response.json();
	return data.data.id;
}

async function sendToFishbushApi(
	data: Registration,
	ownReferralCode: string,
	newUUID: string,
): Promise<FBids> {
	const baseUrl = getBaseUrl();

	// step 1: create or find teacher contact
	const teacherId = await createOrFindTeacherContact(
		data.teacher,
		Number(data.school.id),
		baseUrl,
	);
	// step 2: create contestants
	const contestantIds = await Promise.all(
		data.contestants
			.filter(
				(contestant) =>
					contestant.firstname && contestant.lastname && contestant.email,
			)
			.map(async (contestant, index) => {
				const role =
					index === 0 && data.hasCaptain
						? Enum_Contestant_Role.TeamLeader
						: index < data.contestantCount
							? Enum_Contestant_Role.TeamMember
							: Enum_Contestant_Role.TeamSubstitute;
				return await createContestant(
					contestant,
					Number(data.school.id),
					role,
					baseUrl,
				);
			}),
	);
	const usedReferral =
		6 <= data.referralCode.length && data.referralCode.length >= 10
			? data.referralCode
			: null;
	// step 3: create team
	const teamId = await createTeam(
		data.teamName,
		teacherId,
		contestantIds,
		ownReferralCode,
		usedReferral,
		data.allowedPartnerDataAccess,
		newUUID,
		baseUrl,
	);

	return { teacherId, contestantIds, teamId };
}

async function rollbackFishbushApi(FBids: FBids) {
	const baseUrl = getBaseUrl();

	const teacherUrl = new URL(`api/school-contacts/${FBids.teacherId}`, baseUrl);
	await fetch(teacherUrl.href, {
		method: "DELETE",
		headers: {
			Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
		},
	});

	const contestantUrls = FBids.contestantIds.map(
		(id) => new URL(`api/contestants/${id}`, baseUrl),
	);
	await Promise.all(
		contestantUrls.map(async (url) => {
			await fetch(url.href, {
				method: "DELETE",
				headers: {
					Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
				},
			});
		}),
	);

	const teamUrl = new URL(`api/tda-teams/${FBids.teamId}`, baseUrl);
	await fetch(teamUrl.href, {
		method: "DELETE",
		headers: {
			Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
		},
	});
}

async function sendToGhostApi(data: Registration) {
	const tournamentId = 3733;
	const fallbackGhostSchoolId = 13391;
	const url = new URL("https://tourdeapp.cz/registrace-tymu-api");
	const params = new URLSearchParams();
	params.append("team-name", data.teamName);
	params.append(
		"school-id",
		data.school.attributes?.ghostId?.toString() ??
			fallbackGhostSchoolId.toString(),
	);
	params.append("tournament-id", tournamentId.toString());
	params.append("teacher[name]", data.teacher.firstname);
	params.append("teacher[surname]", data.teacher.lastname);
	params.append("teacher[email]", data.teacher.email);
	params.append("teacher[phone]", data.teacher.phone);
	data.contestants.forEach((contestant, index) => {
		params.append(`contestant[${index}][name]`, contestant.firstname);
		params.append(`contestant[${index}][surname]`, contestant.lastname);
		params.append(`contestant[${index}][email]`, contestant.email);
		params.append(`contestant[${index}][phone]`, contestant.phone);
	});
	if (data.invoice) {
		params.append("invoice[name]", data.invoice.name);
		params.append("invoice[email]", data.invoice.email);
		params.append("invoice[street]", data.invoice.street);
		params.append("invoice[city]", data.invoice.city);
		params.append("invoice[zip]", data.invoice.zip);
		params.append("invoice[ic]", data.invoice.ic);
		params.append("invoice[dic]", data.invoice.dic);
	}
	const response = await fetch(url.href, {
		method: "POST",
		headers: {
			"Content-Type": "application/x-www-form-urlencoded",
		},
		body: params,
	});
	if (!response.ok) {
		throw new Error(`Ghost API failed with status ${response.status}`);
	}
}

async function sendDiscordWebhook(req: Registration) {
	const config = useRuntimeConfig();
	const webhookUrl = config.DISCORD_WEBHOOK_URL;

	try {
		await fetch(webhookUrl, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				content: "Nový tým, jupí!",
				embeds: [
					{
						title: req.teamName,
						description: `${req.contestants.map((contestant) => `- ${contestant.firstname} ${contestant.lastname}`).join("\n")}\n\nZe školy: ${req.school.attributes?.name ?? "Neznámá škola"}`,
						color: 0xef8a17, // Hex color converted to decimal
					},
				],
			}),
		});
	} catch (error) {
    console.error(`[${new Date().toLocaleString()}] ${error}`);
	}
}

export default defineEventHandler(async (event) => {
	const req = (await readBody(event)) as Registration;
	// strip all trailing whitespace from all strings, including nested objects
	req.teamName = req.teamName.trim();
	for (const contestant of req.contestants) {
		contestant.firstname = contestant.firstname.trim();
		contestant.lastname = contestant.lastname.trim();
		contestant.email = contestant.email.trim();
	}
	req.teacher.firstname = req.teacher.firstname.trim();
	req.teacher.lastname = req.teacher.lastname.trim();
	req.teacher.email = req.teacher.email.trim();
	req.teacher.phone = req.teacher.phone.trim();
	req.referralCode = req.referralCode.trim();
	if (req.invoice) {
		req.invoice.name = req.invoice.name.trim();
		req.invoice.email = req.invoice.email.trim();
		req.invoice.street = req.invoice.street.trim();
		req.invoice.city = req.invoice.city.trim();
		req.invoice.zip = req.invoice.zip.trim();
		req.invoice.ic = req.invoice.ic.trim();
		req.invoice.dic = req.invoice.dic.trim();
	}
	

	let FBids: FBids;
	let KCids: KCids;

	const ownReferralCode = generateReferralCode(5);
	const newUUID = uuidv4();
	// step 0: get all contestants with the filter of emails
	try {
		const contestantEmails = req.contestants
			.filter(
				(contestant) =>
					contestant.firstname && contestant.lastname && contestant.email,
			)
			.map((contestant) => contestant.email);
		if (contestantEmails.length < 1) {
			throw new Error("No contestants provided");
		}

		// send a GET request to the Fishbush API to get all contestants with the filter of emails to check if they are already registered
		const duplicateContestants =
			await getContestantDuplicates(contestantEmails);
		if (duplicateContestants.length > 0) {
			return {
				statusCode: 400,
				referralCode: "",
				duplicateContestants: duplicateContestants,
			};
		}
	} catch (e) {
		console.error(e);
		throw new Error(`Failed to get contestants: ${e}`);
	}

	// step 1: send to Fishbush API
	try {
		FBids = await sendToFishbushApi(req, ownReferralCode, newUUID);
	} catch (e) {
		console.error(e);
		throw new Error(`Failed to register team to FishBush: ${e}`);
	}
	// step 2: send to Keycloak API. Fail -> delete from Fishbush API
	try {
		KCids = await sendToKeycloak(req, newUUID);
	} catch (e) {
		await rollbackFishbushApi(FBids);
		console.error(e);
		throw new Error(`Failed to register team to Keycloak: ${e}`);
	}
	// step 3: send to Ghost API. Fail -> delete from Fishbush API, Keycloak API
	try {
		await sendToGhostApi(req);
	} catch (e) {
		await rollbackKeycloakApi(KCids);
		await rollbackFishbushApi(FBids);
		console.error(e);
		throw new Error(`Failed to register team to Ghost: ${e}`);
	}

	// step 4: send Discord webhook message
	try {
		await sendDiscordWebhook(req);
	} catch (e) {
		console.error(e); // only log, do not throw error
	}

	// return 200
	return {
		statusCode: 200,
		referralCode: ownReferralCode,
		duplicateContestants: [],
	};
});
