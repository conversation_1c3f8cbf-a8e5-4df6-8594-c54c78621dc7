import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, sendStream } from "h3";
import { normalize } from "node:path";
import { getBaseUrl } from "@/utils/dataUtils";
const { FISHBUSH_API_URL } = useRuntimeConfig().public

export default defineEventHandler(async (event) => {

  // Get the file path from the URL
  const path = event.context.params?.path;
  if (!path) {
    throw createError({
      statusCode: 400,
      statusMessage: "Bad Request: No file path provided.",
    });
  }

  const normalizedPath = normalize(path).replace(/^(\.\.(\/|\\|$))+/, "");

  // Build the URL to fetch the file from the CMS
  const cmsFileUrl = `${FISHBUSH_API_URL}/uploads/${normalizedPath}`;

  try {
    // Fetch the file from the CMS
    const response = await fetch(cmsFileUrl);

    if (!response.ok) {
      throw createError({
        statusCode: response.status,
        statusMessage: response.statusText,
      });
    }

    // **Add Null Check for response.body**
    if (response.body === null) {
      throw createError({
        statusCode: 500,
        statusMessage: "Internal Server Error: Response body is null.",
      });
    }

    // Get the content type and other headers
    const contentType = response.headers.get("content-type") || "application/octet-stream";
    const contentLength = response.headers.get("content-length");
    const cacheControl = response.headers.get("cache-control") || "public, max-age=31536000, immutable";

    // Set headers on the response
    event.node.res.setHeader("Content-Type", contentType);
    if (contentLength) {
      event.node.res.setHeader("Content-Length", contentLength);
    }
    event.node.res.setHeader("Cache-Control", cacheControl);

    // Stream the response
    return sendStream(event, response.body);
  } catch (error) {
    console.error(`[${new Date().toLocaleString()}] ${error}`);
    throw createError({
      statusCode: 404,
      statusMessage: "File not found.",
    });
  }
});
