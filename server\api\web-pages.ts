// /server/api/web-pages.ts
import { createError, define<PERSON>ventH<PERSON><PERSON>, type H3Event } from "h3";
import { stringify } from "qs";
import { processHtmlContent, getBaseUrl } from "@/utils/dataUtils";
const { FISHBUSH_API_KEY_TRUSTED } = useRuntimeConfig();

export default defineEventHandler(async (event) => {
  function customError(code = 404, message = 'Page not found') {
    return sendError(event, createError({ statusCode: code, statusMessage: message }));
  }

	const baseUrl = getBaseUrl();

	const fullUrl = new URL("api/web-pages", baseUrl);

  // Get query parameters from the incoming request
  const incomingQuery = getQuery(event);

  // Extract the locale from the query parameters, default to "cs" if not provided
  const locale = incomingQuery.locale?.toString() || "cs";

	// Extract the slug from the query parameters
	const slug = incomingQuery.slug?.toString();

	// if slug is not provided, return 404
	if (!slug) {
		return customError();
	}

	const queryParams: FBQueryParams = {
		populate: {
			blocks: {
				populate: "*",
				filters: {
					publishDate: { $lte: new Date().toISOString() },
					$or: [
						{ unpublishDate: { $gte: new Date().toISOString() } },
						{ unpublishDate: { $null: true } },
					],
				},
			},
		},
		filters: {
			slug: {
				$eq: slug,
			},
		},
		locale: locale
	};

	// Serialize query parameters
	const queryString = stringify(queryParams, { encodeValuesOnly: true });
	fullUrl.search = `?${queryString}`;

  try {
    const response = await fetch(fullUrl.href, {
      headers: {
        Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
      },
    });

    if (!response.ok) {
      return customError(response.status);
    }

    const json = await response.json();

		// it might be an article, if there are no data
		if (json.data.length === 0) {
      // count the number of slashes in the slug
      if (slug.split("/").length < 3) {
        return customError();
      }

			const pageWithPossibleArticle = await fetchArticle(slug, locale);

			if (!pageWithPossibleArticle) {
        return customError();
      } 
      
      const article = extractArticle(pageWithPossibleArticle);

      return processArticle(article, locale);
		}

		// Existing logic for /it is a web page
		return processWebPages(json, locale, event);

  } catch (error) {
    console.error(`[${new Date().toLocaleString()}] ${error}`);
    return customError(500, `${error}`);
  }
});

async function fetchArticle(slug: string, locale: string) {
  const articleSlug = `/${slug.split("/").pop()}`
  const categorySlug = slug.split("/").slice(0, -1).join("/");
  const baseUrl = getBaseUrl();
  const fullUrl = new URL("api/web-pages", baseUrl);
  const queryParams: FBQueryParams = {
		populate: {
			blocks: {
        on: {
          "content-blocks.article-list": {
            fields: ["publishDate", "unpublishDate"],
            filters: {
              publishDate: { $lte: new Date().toISOString() },
              $or: [
                { unpublishDate: { $gte: new Date().toISOString() } },
                { unpublishDate: { $null: true } },
              ],
            },
            populate: {
              categories: {
                fields: ["name"],
								populate: {
									articles: {
										fields: ["title", "slug", "publishDate", "unpublishDate", "anotation", "content", "showAuthor", "hasFullHtmlControl"],
										filters: {
											publishDate: { $lte: new Date().toISOString() },
											$or: [
												{ unpublishDate: { $gte: new Date().toISOString() } },
												{ unpublishDate: { $null: true } },
											],
                      slug: {
                        $eq: articleSlug,
                      }
										},
										populate: {
											author: {
												fields: ["firstname", "lastname"]
											}
										},
									},
								},
							}
            }
          }
        },
			},
		},
		filters: {
			slug: {
				$eq: categorySlug,
			},
		},
		locale: locale
	};

  // Serialize query parameters
  const queryString = stringify(queryParams, { encodeValuesOnly: true });
  fullUrl.search = `?${queryString}`;

  try {
    const response = await fetch(fullUrl.href, {
      headers: {
        Authorization: `Bearer ${FISHBUSH_API_KEY_TRUSTED}`,
      },
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data.data[0];
  } catch (error) {
    console.error(`[${new Date().toLocaleString()}] ${error}`);
    throw createError({
      statusCode: 500,
      statusMessage: `${error}`,
    });
  }
}

// biome-ignore lint/suspicious/noExplicitAny: Unsafe JSON parsing
function extractArticle(pageWithPossibleArticle: any) {
  const articleListBlocks = pageWithPossibleArticle?.attributes?.blocks;
  if (!articleListBlocks) {
    return null;
  }

  for (const articleListBlock of articleListBlocks) {
    if (!articleListBlock.categories?.data) continue;
    for (const category of articleListBlock.categories.data) {
      if (!category?.attributes?.articles?.data || !category?.attributes?.name) continue;
      const categoryName = category.attributes.name;
      for (const article of category.attributes.articles.data) {
        if (!article?.attributes) continue;
        article.attributes.category = categoryName;
        return article;
      }
    }
  }  
  return null // no article found
}

// Helper function to process /web-pages data
// biome-ignore lint/suspicious/noExplicitAny: Unsafe JSON parsing
function processWebPages(json: any, locale: string, event: H3Event) {
  const now = new Date();

  const isPublished = (
    publishDate: string | undefined,
    unpublishDate: string | undefined
  ): boolean => {
    const publish = publishDate ? new Date(publishDate) <= now : true;
    const unpublish = unpublishDate ? new Date(unpublishDate) >= now : true;
    return publish && unpublish;
  };

  type ContentBlock = {
    publishDate?: string;
    unpublishDate?: string;
    content?: Array<ContentBlock | string | undefined> | string | undefined;
    
    // biome-ignore lint/suspicious/noExplicitAny: Allow for other properties
    [key: string]: any; // 
  };

  // Helper function to process content blocks
  function processContentBlock(block: ContentBlock) {
    if (Array.isArray(block.content)) {
      // **Filter sub-blocks based on their publish/unpublish dates**
      block.content = block.content
        .filter((subBlock) => {
          if (typeof subBlock === "object" && subBlock !== null) {
            return isPublished(subBlock.publishDate, subBlock.unpublishDate);
          }
          return true; // Keep the sub-block if it's not an object
        })
        .map((subBlock) => {
          if (typeof subBlock === "object" && subBlock !== null) {
            processContentBlock(subBlock);
          }
          return subBlock;
        });

			// pick the newest block from the array
			block.content = block.content.sort((a: string | ContentBlock | undefined, b: string | ContentBlock | undefined) => {
				// if a or b is string or undefined, return 0
				if (!a || typeof a === "string" || !b || typeof b === "string") {
					return 0;
				}
				// if a.publishDate is not defined, treat it as 1970-01-01
				const aDate = new Date(a.publishDate || "1970-01-01");
				const bDate = new Date(b.publishDate || "1970-01-01");
				return bDate.getTime() - aDate.getTime();
			}
			);
    } else if (typeof block.content === "string") {
      // Process the HTML content
      block.content = processHtmlContent(block.content, locale);
    }
  }

	for (const item of json.data) {
    item.attributes.blocks = item.attributes.blocks
      .map((block: ContentBlock) => {
        // Process the content block to modify links and filter sub-blocks
        processContentBlock(block);
        return block;
      });
		
		// for each block, if its content is an array, pick the first element and assign it as the block itself
		item.attributes.blocks = item.attributes.blocks.map((block: ContentBlock) => {
			if (Array.isArray(block.content)) {
				if (typeof block.content[0] === "string" || !block.content[0]) return block;
				block.content[0].__component = "content-blocks.basic-content"
				return block.content[0];
			}
			return block;
		});

		// page exists, but has no content. Does it have a redirect URL?
    if (item.attributes.blocks.length === 0) {
      if (item.attributes.noContentRedirectUrl) {
        return { redirectUrl: item.attributes.noContentRedirectUrl };
      }
      // Throw a 404 error - no redirect URL
      return sendError(event, createError({ statusCode: 404, statusMessage: 'Not Found' }));
    }
  }

  return json;
}

// Helper function to process /web-articles data
// biome-ignore lint/suspicious/noExplicitAny: Unsafe JSON parsing
function processArticle(articleData: any, locale: string) {

  const att = articleData.attributes;
  // Process the article content
  const content = processHtmlContent(att.content, locale);
  const anotation = processHtmlContent(att.anotation, locale);

  // Create a content block with __component: 'content-blocks.article'
  const contentBlock = {
    __component: "content-blocks.article",
    id: articleData.id,
    publishDate: att.publishDate,
    unpublishDate: att.unpublishDate,
    content: content,
    anotation: anotation,
    title: att.title,
    hasFullHtmlControl: att.hasFullHtmlControl,
    category: att.category,
    showAuthor: att.showAuthor,
    author: att.showAuthor ? att.author?.data?.attributes : null
  };

  let plainTextAnotation = anotation.replace(/<[^>]*>/g, "");
  // if the anotation is too long, cut it to 160 characters and add "..." as 8230 character escaped
  if (plainTextAnotation.length > 160) {
    plainTextAnotation = `${plainTextAnotation.substring(0, 160)}\u2026`;
  }

  // Construct the page data
  const pageData = {
    data: [
      {
        id: articleData.id,
        attributes: {
          title: articleData.attributes.title,
          metaDescription: plainTextAnotation,
          createdAt: articleData.attributes.createdAt,
          updatedAt: articleData.attributes.updatedAt,
          slug: articleData.attributes.slug,
          blocks: [contentBlock],
        },
      },
    ],
    meta: {},
  };

  return pageData;
}