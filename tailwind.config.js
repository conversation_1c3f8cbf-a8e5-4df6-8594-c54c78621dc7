/** @type {import('tailwindcss').Config} */
import { fontFamily } from "tailwindcss/defaultTheme";

export default {
	content: [
		"./components/**/*.{js,vue,ts}",
		"./layouts/**/*.vue",
		"./pages/**/*.vue",
		"./plugins/**/*.{js,ts}",
		"./app.vue",
		"./error.vue",
	],
	safelist: [
		{
			pattern:
				/^(w|justify|h|items|m|mt|mb|ml|mr|mx|my|p|pt|pb|pl|pr|px|py|bg)-./,
			variants: ["xs", "sm", "md", "lg", "xl", "2xl"],
		},
	],
	theme: {
		fontFamily: {
			sans: ["Inter Tight", ...fontFamily.sans],
			mono: ["Inconsolata", ...fontFamily.mono],
		},
		screens: {
			xs: "375px",
			sm: "640px",
			md: "768px",
			lg: "1024px",
			xl: "1280px",
			"2xl": "1536px",
		},
		extend: {
			colors: {
				tda: {
					DEFAULT: "#EF8A17",
					100: "#F19E18",
					200: "#EF8A17",
					300: "#ED7517",
					400: "#EC6116",
					500: "#EA4C15",
					600: "#E83715",
					700: "#E62314",
				},
				informative: "#FFFFFF",
				background: "#0B0F17",
			},
			typography: (theme) => ({
				DEFAULT: {
					css: {
						color: theme("colors.informative"),
						a: {
							color: theme("colors.tda.300"),
							"&:hover": {
								color: theme("colors.tda.DEFAULT"),
							},
						},
						h1: {
							color: theme("colors.tda.DEFAULT"),
						},
						h2: {
							color: theme("colors.tda.200"),
						},
						h3: {
							color: theme("colors.tda.300"),
						},
						h4: {
							color: theme("colors.tda.400"),
						},
						h5: {
							color: theme("colors.tda.500"),
						},
						h6: {
							color: theme("colors.tda.600"),
						},
					},
				},
			}),
		},
	},
	plugins: [
		require("tailwind-scrollbar")({ nocompatible: true }),
		require("@tailwindcss/forms"),
	],
};
