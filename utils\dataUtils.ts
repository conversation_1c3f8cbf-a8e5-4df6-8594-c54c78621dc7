import { load } from 'cheerio';
const { FISHBUSH_API_URL } = useRuntimeConfig().public;

export type ImageBulk = {
	defaultImageUrl: string;
	thumbnailImageUrl: string;
	smallImageUrl: string;
	mediumImageUrl: string;
	largeImageUrl: string;
};

function ensureTrailingSlash(url: string): string {
  return url.replace(/\/?$/, "/");
}

export function getBaseUrl(): string {
	return ensureTrailingSlash(FISHBUSH_API_URL);
}

export function createImageUrls(fileEntity: UploadFileEntity) {

	function createImageUrl(relativeUrl: string | null) {
		if (typeof relativeUrl !== "string" || !relativeUrl) return null;

		// Ensure baseUrl ends with a slash
		const baseUrl = getBaseUrl();

		// Remove leading slash from the relativeUrl to avoid overwriting the baseUrl path
		const sanitizedRelativeUrl = relativeUrl.replace(/^\//, "");

		return new URL(sanitizedRelativeUrl, baseUrl).href;
	}

	if (!fileEntity) throw Error("Incomplete image file");
	if (!fileEntity.attributes || !fileEntity.attributes.url)
		throw Error("Incomplete image file");
	const defaultImageUrl = createImageUrl(fileEntity.attributes.url);
	if (!defaultImageUrl) throw Error("Incomplete image file");

	const formats = fileEntity.attributes.formats;
	const thumbnailImageUrl = createImageUrl(formats?.thumbnail?.url ?? null) ?? defaultImageUrl;
	const smallImageUrl =	createImageUrl(formats?.small?.url ?? null) ?? thumbnailImageUrl;
	const mediumImageUrl = createImageUrl(formats?.medium?.url ?? null) ?? smallImageUrl;
	const largeImageUrl =	createImageUrl(formats?.large?.url ?? null) ?? mediumImageUrl;

	const imageBulk: ImageBulk = {
		defaultImageUrl: defaultImageUrl,
		thumbnailImageUrl: thumbnailImageUrl,
		smallImageUrl: smallImageUrl,
		mediumImageUrl: mediumImageUrl,
		largeImageUrl: largeImageUrl,
	};

	return imageBulk;
}

export function processHtmlContent(htmlContent: string, locale: string): string {
  const $ = load(htmlContent);
	
  // Modify relative links to include locale prefix if necessary
  $("a[href]").each((_, element) => {
    const href = $(element).attr("href");
    if (href && !/^[a-zA-Z][a-zA-Z\d+\-.]*:/.test(href)) {
      // This is a relative link
      if (locale !== "cs") {
        const localePrefix = `/${locale}`;
        if (!href.startsWith(`${localePrefix}/`) && href !== localePrefix) {
          const newHref = `${localePrefix}${href.startsWith("/") ? "" : "/"}${href}`;
          $(element).attr("href", newHref);
        }
      }
    }
  });

  // Replace absolute CMS file URLs with your API endpoint
  $("img[src], a[href]").each((_, element) => {
    const attr = $(element).is("img") ? "src" : "href";
    const url = $(element).attr(attr);

    if (url?.startsWith(`${FISHBUSH_API_URL}/uploads/`)) {
      // Replace with your API endpoint
      const relativePath = url.replace(FISHBUSH_API_URL, "");
      const newUrl = `/api${relativePath}`;
      $(element).attr(attr, newUrl);
    }
  });

  // Get the modified HTML
  const modifiedHtml = $('body').html()?.toString() || '';
  return modifiedHtml;
}
