export function getPathWithoutLocale(path: string, locale: string, defaultLocale: string): string {
  if (locale !== defaultLocale) {
    const prefix = `/${locale}`;
    if (path.startsWith(prefix)) {
      return path.slice(prefix.length) || '/';
    }
  }
  return path;
}

export function getLocalizedPath(path: string, locale: string, defaultLocale: string): string {
  if (locale !== defaultLocale) {
    return `/${locale}${path}`;
  }
  return path;
}